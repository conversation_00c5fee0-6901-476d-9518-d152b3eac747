# Environment variables for Unified ReAct Agent
# Copy this file to .env and update with your actual values

# Python path (required for module resolution)
PYTHONPATH=src

# Mistral AI Configuration
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-large-latest
MISTRAL_GITLAB_MODEL=mistral-medium

# GitLab Configuration
GITLAB_URL=https://gitlab.com
GITLAB_PROJECT_ID=your_project_id_here
GITLAB_TOKEN=your_gitlab_token_here

# SQL Server Configuration
SQL_SERVER=localhost
SQL_DATABASE=STAGE
SQL_TRUSTED_CONNECTION=yes
# SQL_USERNAME=your_username  # only needed if SQL_TRUSTED_CONNECTION=no
# SQL_PASSWORD=your_password  # only needed if SQL_TRUSTED_CONNECTION=no

# File System Configuration
PCAP_FOLDER=pcap
LOG_FOLDER=logs
MAX_LOG_LINES=1000
MAX_SEARCH_RESULTS=100

# Agent Configuration
ENABLE_SQL=true
AGENT_VERBOSE=true
AGENT_DEBUG=false

# Logging Configuration
LOG_LEVEL=INFO
# LOG_FILE=agent.log  # optional log file

# OpenAI Configuration (fallback for other agents)
OPENAI_API_KEY=your_openai_api_key_here
