# Unified ReAct Agent - Quick Start Guide

## 🚀 Setup

1. **Install dependencies** (if not already done):
```bash
pip install poetry
poetry install
```

2. **Configure environment variables**:
```bash
cp .env.unified_template .env
# Edit .env with your actual API keys and configuration
```

3. **Test the agent**:
```bash
python test_unified_agent.py
```

## 🎯 Quick Usage

### Simple Wrapper (Recommended)
```bash
# Single query mode (clean output like v1_agent)
python run_unified_agent.py "your question here"

# Interactive mode
python run_unified_agent.py
```

### Framework CLI (Clean Output)
```bash
# Set Python path and run agent with clean output
$env:PYTHONPATH="src"  # Windows PowerShell
# export PYTHONPATH=src  # Linux/Mac

python -m ai_agents.execution.cli unified_react --clean --input '{"query": "your question"}'
```

### Framework CLI (Verbose Output)
```bash
# Standard framework output with all metadata
python -m ai_agents.execution.cli unified_react --input '{"query": "your question"}'
```

### Example Queries

**GitLab File Operations:**
```bash
# Simple wrapper (clean output like v1_agent)
python run_unified_agent.py "Show me config.py from main branch"
python run_unified_agent.py "Get SHA for utils.py"
python run_unified_agent.py "What was in config.py on 2024-01-15?"

# Framework CLI (clean output)
python -m ai_agents.execution.cli unified_react --clean --input '{"query": "Show me config.py from main branch"}'
```

**SQL Database Queries:**
```bash
# Simple wrapper (clean output like v1_agent)
python run_unified_agent.py "Show all executions for testcase ID 1"
python run_unified_agent.py "What tables are available?"
python run_unified_agent.py "What is the SHA of testcase with id=1?"

# Framework CLI (clean output)
python -m ai_agents.execution.cli unified_react --clean --input '{"query": "List all tables"}'
```

**File System Operations:**
```bash
# Simple wrapper (clean output like v1_agent)
python run_unified_agent.py "List all PCAP files"
python run_unified_agent.py "Search for error in logs"
python run_unified_agent.py "Show me app.log"

# Framework CLI (clean output)
python -m ai_agents.execution.cli unified_react --clean --input '{"query": "List all PCAP files"}'
```

## 🔧 What Was Recreated

From the original v1_agent, I recreated:

### ✅ **Tools (Framework-Compliant)**
- **GitLabSHATool**: Get file SHA hashes
- **GitLabFileTool**: Get file content by SHA
- **GitLabTimestampTool**: Get files from specific timestamps
- **PCAPListTool**: List PCAP files
- **LogListTool**: List log files
- **PCAPInfoTool**: Get PCAP file information
- **LogContentTool**: Read log file content
- **LogSearchTool**: Search across log files
- **SQLSchemaTool**: Get database schema
- **SQLQueryTool**: Execute SQL queries
- **SQLCheckerTool**: Validate SQL queries

### ✅ **Model Integration**
- **MistralLLM**: Custom LangChain wrapper for Mistral API
- **MistralChatModel**: Chat-optimized Mistral model for SQL operations
- **ModelLoader**: Extended to support Mistral models

### ✅ **Agent Implementation**
- **UnifiedReActAgent**: Main agent class inheriting from AgentBase
- **ReAct Pattern**: Full reasoning and acting workflow
- **Error Handling**: Comprehensive error management
- **Configuration**: YAML + environment variable configuration

### ✅ **Framework Integration**
- **Agent Registry**: Registered as "unified_react"
- **Tool Registry**: All tools properly registered
- **CLI Support**: Works with existing CLI infrastructure
- **Configuration System**: Uses framework's config loading

## 🎉 Key Improvements

1. **Framework Compliance**: Follows all framework patterns and interfaces
2. **Modular Design**: Tools are separate, reusable modules
3. **Better Error Handling**: Graceful degradation and clear error messages
4. **Configuration Management**: Centralized configuration with environment variable support
5. **Testing**: Comprehensive test suite included
6. **Documentation**: Full documentation and examples

## 🔍 Architecture Overview

```
UnifiedReActAgent
├── GitLab Tools (gitlab_tools.py)
│   ├── GitLabSHATool
│   ├── GitLabFileTool
│   └── GitLabTimestampTool
├── File System Tools (file_tools.py)
│   ├── PCAPListTool
│   ├── LogListTool
│   ├── PCAPInfoTool
│   ├── LogContentTool
│   └── LogSearchTool
├── SQL Tools (sql_tools.py)
│   ├── SQLSchemaTool
│   ├── SQLQueryTool
│   └── SQLCheckerTool
├── Mistral Models (model/loader.py)
│   ├── MistralLLM
│   └── MistralChatModel
└── ReAct Agent (LangChain AgentExecutor)
```

## 🚨 Important Notes

1. **Environment Variables**: Make sure all required environment variables are set in `.env`
2. **Dependencies**: Ensure `mistralai`, `pyodbc`, and other dependencies are installed
3. **SQL Server**: SQL functionality requires proper ODBC driver and database access
4. **File Folders**: PCAP and log folders will be created if they don't exist
5. **GitLab Access**: Requires valid GitLab token with repository access

The agent is now fully integrated into the framework and ready for use!
