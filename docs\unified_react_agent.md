# Unified ReAct Agent

The Unified ReAct Agent is a comprehensive AI agent that combines GitLab file operations, SQL database queries, and local file system operations using the ReAct (Reasoning and Acting) pattern.

## Features

- **GitLab Integration**: Access repository files, get SHAs, retrieve content by timestamp
- **SQL Database Operations**: Query SQL Server databases with schema inspection and validation
- **File System Operations**: Work with local PCAP and log files
- **ReAct Pattern**: Uses reasoning and acting for complex multi-step operations
- **Mistral AI Integration**: Powered by Mistral AI models for optimal performance

## Configuration

The agent is configured via `config.yaml` and environment variables:

### Environment Variables (.env)

```env
# Mistral AI Configuration
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-large-latest
MISTRAL_GITLAB_MODEL=mistral-medium

# GitLab Configuration
GITLAB_URL=https://gitlab.com
GITLAB_PROJECT_ID=your_project_id_here
GITLAB_TOKEN=your_gitlab_token_here

# SQL Server Configuration
SQL_SERVER=localhost
SQL_DATABASE=STAGE
SQL_TRUSTED_CONNECTION=yes

# File System Configuration
PCAP_FOLDER=pcap
LOG_FOLDER=logs
MAX_LOG_LINES=1000
MAX_SEARCH_RESULTS=100

# Agent Configuration
ENABLE_SQL=true
AGENT_VERBOSE=true
```

### YAML Configuration

The agent uses `src/ai_agents/agents/unified_react_agent/config.yaml` for detailed configuration including model settings, tool configurations, and behavior parameters.

## Available Tools

### GitLab Tools
- `get_file_sha`: Get SHA hash of a file at a specific commit reference
- `get_file_sha_from_branch_timestamp`: Get SHA hash from branch before timestamp
- `get_file_content`: Get file content using SHA hash
- `extract_info`: Extract structured information from user queries

### SQL Database Tools
- `sql_db_query`: Execute SQL queries
- `sql_db_schema`: Get schema information for tables
- `sql_db_list_tables`: List all tables in the database
- `sql_db_query_checker`: Verify SQL queries before execution

### File System Tools
- `list_pcap_files`: List PCAP files in configured folder
- `list_log_files`: List log files in configured folder
- `get_pcap_info`: Get detailed information about PCAP files
- `get_log_content`: Read log file content with line limiting
- `search_logs`: Search for terms across all log files

### Utility Tools
- `final_answer_tool`: Format and provide final answers

## Usage Examples

### Running via CLI

```bash
# Set PYTHONPATH and run agent
export PYTHONPATH=src  # On Windows: $env:PYTHONPATH="src"
python -m ai_agents.execution.cli unified_react --input '{"query": "your question here"}'
```

### Example Queries

**GitLab Operations:**
```bash
# Get file content from main branch
python -m ai_agents.execution.cli unified_react --input '{"query": "Show me the content of config.py from main branch"}'

# Get file SHA
python -m ai_agents.execution.cli unified_react --input '{"query": "Get SHA for utils.py on develop branch"}'

# Get file content from specific timestamp
python -m ai_agents.execution.cli unified_react --input '{"query": "What was in config.py from main branch on 2024-01-15T10:30:00Z?"}'
```

**SQL Operations:**
```bash
# Query database
python -m ai_agents.execution.cli unified_react --input '{"query": "Show all executions for testcase ID 1"}'

# Get schema information
python -m ai_agents.execution.cli unified_react --input '{"query": "What is the schema of the execution table?"}'
```

**File System Operations:**
```bash
# List PCAP files
python -m ai_agents.execution.cli unified_react --input '{"query": "List all PCAP files"}'

# Search logs
python -m ai_agents.execution.cli unified_react --input '{"query": "Search for error in all log files"}'

# Get log content
python -m ai_agents.execution.cli unified_react --input '{"query": "Show me the last 100 lines of app.log"}'
```

### Programmatic Usage

```python
from ai_agents.agents.registry import get_agent

# Create agent instance
agent = get_agent("unified_react")

# Run a query
result = agent.run({"query": "Show me README.md from main branch"})

print(f"Response: {result['response']}")
print(f"Status: {result['metadata']['status']}")
```

## Architecture

The Unified ReAct Agent follows the framework's architecture:

1. **Inherits from AgentBase**: Implements the standard agent interface
2. **Uses LangChain ReAct**: Employs the ReAct pattern for reasoning and acting
3. **Modular Tools**: Each tool type (GitLab, SQL, File) is implemented as separate modules
4. **Configuration-Driven**: All settings are externalized to YAML and environment variables
5. **Error Handling**: Comprehensive error handling with graceful degradation

## Tool Implementation

All tools follow the framework's `BaseTool` interface:

- **GitLab Tools** (`src/ai_agents/tools/gitlab_tools.py`): Handle repository operations
- **File Tools** (`src/ai_agents/tools/file_tools.py`): Handle local file operations
- **SQL Tools** (`src/ai_agents/tools/sql_tools.py`): Handle database operations

## Model Integration

The agent uses Mistral AI models through custom LangChain wrappers:

- **MistralLLM**: For general operations and GitLab tasks
- **MistralChatModel**: Optimized for SQL operations with chat interface

## Dependencies

The agent requires these additional dependencies:
- `mistralai`: For Mistral AI API access
- `pyodbc`: For SQL Server connectivity
- `requests`: For GitLab API calls
- `glob`: For file pattern matching

## Error Handling

The agent includes comprehensive error handling:
- **GitLabAPIError**: For GitLab-specific errors
- **FileSystemError**: For file system operation errors
- **SQLDatabaseError**: For database operation errors
- **Graceful Degradation**: Continues operation even if some tools fail to initialize

## Testing

Run the test suite to verify functionality:

```bash
python test_unified_agent.py
```

This will test:
- Agent creation and initialization
- Tool availability and functionality
- Configuration loading
- Basic query processing
