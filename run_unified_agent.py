#!/usr/bin/env python3
"""
Simple wrapper script for the Unified ReAct Agent.

This provides the same clean interface as the original v1_agent.
Usage:
    python run_unified_agent.py                    # Interactive mode
    python run_unified_agent.py "your query here"  # Single query mode
"""

import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).resolve().parent / "src"))

from ai_agents.agents.registry import get_agent
from ai_agents.utils.config_loader import load_env_vars


def print_banner():
    """Print the agent banner."""
    print("🤖 Unified ReAct Agent")
    print("=" * 60)
    print("GitLab + SQL + File System Operations")
    print("Powered by Mistral AI & LangChain ReAct")
    print("=" * 60)


def check_environment():
    """Check if required environment variables are set."""
    env_vars = load_env_vars()
    
    missing_vars = []
    if not env_vars.get("MISTRAL_API_KEY"):
        missing_vars.append("MISTRAL_API_KEY")
    
    if missing_vars:
        print("❌ Configuration Error:")
        for var in missing_vars:
            print(f"   Missing environment variable: {var}")
        print("\n💡 Please set these in your .env file")
        print("   You can copy .env.unified_template to .env and update it")
        return False
    
    return True


def create_agent():
    """Create and return the agent instance."""
    try:
        agent = get_agent("unified_react")
        print(f"✅ Agent initialized successfully!")
        print(f"   Tools available: {len(agent.tools)}")
        print(f"   SQL enabled: {agent.db is not None}")
        return agent
    except Exception as e:
        print(f"❌ Error initializing agent: {str(e)}")
        return None


def run_single_query(agent, query):
    """Run a single query and print the result."""
    print(f"\n🔍 Query: {query}")
    print("-" * 60)
    
    try:
        response = agent.process_query(query)
        print(f"\n📋 Response:")
        print(response)
    except Exception as e:
        print(f"❌ Error: {str(e)}")


def run_interactive_mode(agent):
    """Run the agent in interactive mode."""
    print("\n💡 Example queries you can try:")
    examples = [
        "What is the SHA of testcase with id=1?",
        "Show me the schema of the testcase table",
        "List all PCAP files",
        "Search for 'error' in log files",
        "Show me README.md from main branch"
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"   {i}. {example}")
    
    print("\nType your query (or 'quit' to exit):")
    print("=" * 60)
    
    while True:
        try:
            user_input = input("\n🔍 Query: ")
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not user_input.strip():
                continue
            
            print("\n🤔 Processing...")
            
            try:
                response = agent.process_query(user_input)
                print(f"\n📋 Response:")
                print("-" * 40)
                print(response)
                print("-" * 40)
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {str(e)}")


def main():
    """Main entry point."""
    print_banner()
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Create agent
    agent = create_agent()
    if not agent:
        sys.exit(1)
    
    # Check if query provided as command line argument
    if len(sys.argv) > 1:
        # Single query mode
        query = " ".join(sys.argv[1:])
        run_single_query(agent, query)
    else:
        # Interactive mode
        run_interactive_mode(agent)


if __name__ == "__main__":
    main()
