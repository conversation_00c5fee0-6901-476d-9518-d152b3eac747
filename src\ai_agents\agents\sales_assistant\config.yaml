workflow:
  entrypoint: start
  error_node: error_handler

  nodes:
    - name: start
      handler: start_node
      type: entry
    - name: query_db
      handler: query_database
      type: function
    - name: decide
      handler: summarize_answer
      type: decision
    - name: finish
      handler: end_node
      type: exit

  edges:
      - from: start
        to: query_db
      - from: query_db
        to: decide

state_schema:
  customer_query: string
  db_result: object
  summary: string
  result: string
  error: string

memory:
  type: vector
  config:
    embedding_model: openai
    store_path: ./vector_store

model:
  type: openai
  model_id: llama3.3-70b
  config:
    temperature: 0.7
    max_tokens: 1000
    openai_api_base: https://llm-proxy.kpit.com
