"""Supervisor agent that manages and reviews sales assistant responses."""

import os
from langgraph.graph import StateGraph
from ai_agents.agents.workflow_agent import WorkflowAgent
from . import handlers


class SupervisorAgent(WorkflowAgent):
    """
    SupervisorAgent manages and reviews responses from the sales assistant.

    The agent:
    1. Processes input queries
    2. Delegates to sales assistant
    3. Reviews responses
    4. Provides final reviewed output

    Attributes:
        default_config_path (str): Path to the default configuration file
    """

    default_config_path = os.path.join(os.path.dirname(__file__), "config.yaml")

    @property
    def handler_module(self):
        """Return the Python module containing agent-specific handlers."""
        return handlers

    @property
    def prompt_dir(self):
        """Return the directory path for prompt templates."""
        return os.path.join(os.path.dirname(__file__), "prompts")

    def build_agent(self) -> StateGraph:
        """
        Build and return the workflow graph for the supervisor agent.

        Returns:
            StateGraph: The agent's workflow graph implementation that defines its behavior
        """
        return self._build_workflow_graph(
            self.config, self.handler_module, self.prompt_dir
        )
