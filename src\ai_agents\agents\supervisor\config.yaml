workflow:
  entrypoint: start
  error_node: error_handler

  nodes:
    - name: start
      handler: start_node
      type: entry
    - name: sales_assistant_task
      handler: sales_assistant
      type: agent
    - name: review_response
      handler: review_response
      type: decision
    - name: finish
      handler: end_node
      type: exit

  edges:
      - from: start
        to: sales_assistant_task
      - from: sales_assistant_task
        to: review_response
      - from: review_response
        to: finish

state_schema:
  input_query: string
  assistant_response: object
  review_result: string
  final_response: string
  error: string

memory:
  type: vector
  config:
    embedding_model: openai
    store_path: ./vector_store

model:
  type: openai
  model_id: llama3.3-70b
  config:
    temperature: 0.7
    max_tokens: 1000
    openai_api_base: https://llm-proxy.kpit.com
