"""Unified ReAct Agent implementation.

This agent combines GitLab file operations, SQL database queries, and local file system operations
using a ReAct (Reasoning and Acting) pattern with LangChain.
"""

import os
import json
import re
import urllib.parse
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import PromptTemplate
from langchain_core.tools import Tool
from langchain_community.utilities import SQLDatabase
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from ..agent_base import AgentBase


class UnifiedReActAgent(AgentBase):
    """
    Unified ReAct agent for GitLab file operations, SQL database queries, and file system operations.
    
    This agent uses the ReAct (Reasoning and Acting) pattern to handle complex queries
    across multiple systems: GitLab repositories, SQL databases, and local file systems.
    """

    default_config_path = os.path.join(os.path.dirname(__file__), "config.yaml")

    def __init__(self, config: Dict[str, Any]):
        """Initialize the unified ReAct agent."""
        self.config = self._merge_env_config(config)
        self.model = self.build_model()
        self.sql_model = self.build_sql_model()
        self.db = self.build_database()
        self.tools = self.build_tools()
        self.agent = self.build_agent()
        self.clean_agent = None  # Lazy initialization for clean agent

    def _merge_env_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge environment variables into configuration."""
        from ai_agents.utils.config_loader import load_env_vars
        env_vars = load_env_vars()

        # Update GitLab config with environment variables
        gitlab_config = config.get("gitlab", {})
        if env_vars.get("GITLAB_URL"):
            gitlab_config["url"] = env_vars["GITLAB_URL"]
        if env_vars.get("GITLAB_PROJECT_ID"):
            gitlab_config["project_id"] = env_vars["GITLAB_PROJECT_ID"]
        if env_vars.get("GITLAB_TOKEN"):
            gitlab_config["token"] = env_vars["GITLAB_TOKEN"]
        config["gitlab"] = gitlab_config

        # Update model config with environment variables
        model_config = config.get("model", {}).get("config", {})
        if env_vars.get("MISTRAL_API_KEY"):
            model_config["api_key"] = env_vars["MISTRAL_API_KEY"]
        config.setdefault("model", {})["config"] = model_config

        # Update SQL model config with environment variables
        sql_model_config = config.get("sql_model", {}).get("config", {})
        if env_vars.get("MISTRAL_API_KEY"):
            sql_model_config["api_key"] = env_vars["MISTRAL_API_KEY"]
        config.setdefault("sql_model", {})["config"] = sql_model_config

        # Update SQL config with environment variables
        sql_config = config.get("sql", {})
        if env_vars.get("SQL_SERVER"):
            sql_config["server"] = env_vars["SQL_SERVER"]
        if env_vars.get("SQL_DATABASE"):
            sql_config["database"] = env_vars["SQL_DATABASE"]
        if env_vars.get("SQL_TRUSTED_CONNECTION"):
            sql_config["use_trusted_connection"] = env_vars["SQL_TRUSTED_CONNECTION"].lower() == "yes"
        if env_vars.get("SQL_USERNAME"):
            sql_config["username"] = env_vars["SQL_USERNAME"]
        if env_vars.get("SQL_PASSWORD"):
            sql_config["password"] = env_vars["SQL_PASSWORD"]
        if env_vars.get("ENABLE_SQL"):
            sql_config["enabled"] = env_vars["ENABLE_SQL"].lower() == "true"
        config["sql"] = sql_config

        # Update file system config with environment variables
        file_config = config.get("file_system", {})
        if env_vars.get("PCAP_FOLDER"):
            file_config["pcap_folder"] = env_vars["PCAP_FOLDER"]
        if env_vars.get("LOG_FOLDER"):
            file_config["log_folder"] = env_vars["LOG_FOLDER"]
        config["file_system"] = file_config

        # Update agent config with environment variables
        agent_config = config.get("agent", {})
        if env_vars.get("AGENT_VERBOSE"):
            agent_config["verbose"] = env_vars["AGENT_VERBOSE"].lower() == "true"
        config["agent"] = agent_config

        return config

    def build_sql_model(self):
        """Build a separate model for SQL operations."""
        sql_model_cfg = self.config.get("sql_model", {})
        if not sql_model_cfg:
            return self.model  # Fallback to main model
        
        from ai_agents.model.loader import ModelLoader
        from ai_agents.utils.config_loader import load_env_vars
        
        config_dict = dict(sql_model_cfg.get("config", {}))
        if "api_key" not in config_dict:
            env_vars = load_env_vars()
            if env_vars.get("MISTRAL_API_KEY"):
                config_dict["api_key"] = env_vars["MISTRAL_API_KEY"]
        
        return ModelLoader.load_model(
            model_type=sql_model_cfg.get("type", "mistral-chat"),
            model_id=sql_model_cfg.get("model_id", "mistral-large-latest"),
            config=config_dict,
        )

    def build_database(self):
        """Build database connection if SQL is enabled."""
        sql_config = self.config.get("sql", {})
        if not sql_config.get("enabled", False):
            return None
        
        try:
            connection_string = self._build_connection_string(sql_config)
            params = urllib.parse.quote_plus(connection_string)
            
            return SQLDatabase.from_uri(
                f"mssql+pyodbc:///?odbc_connect={params}",
                include_tables=sql_config.get("tables", ["execution", "testcase", "run"])
            )
        except Exception as e:
            print(f"Warning: Could not initialize SQL database: {e}")
            return None

    def _build_connection_string(self, sql_config: Dict[str, Any]) -> str:
        """Build SQL Server connection string from configuration."""
        server = sql_config.get("server", "localhost")
        database = sql_config.get("database", "STAGE")
        use_trusted = sql_config.get("use_trusted_connection", True)
        
        if use_trusted:
            return (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={server};"
                f"DATABASE={database};"
                f"Trusted_Connection=yes;"
            )
        else:
            username = sql_config.get("username", "")
            password = sql_config.get("password", "")
            return (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={server};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
            )

    def build_tools(self) -> List[Tool]:
        """Build all tools for the agent."""
        tools = []
        
        # Add GitLab tools
        tools.extend(self._create_gitlab_tools())
        
        # Add File System tools
        tools.extend(self._create_file_tools())
        
        # Add SQL tools if database is available
        if self.db:
            tools.extend(self._create_sql_tools())
        
        # Add utility tools
        tools.extend(self._create_utility_tools())
        
        return tools

    def _create_gitlab_tools(self) -> List[Tool]:
        """Create GitLab-related tools."""
        from ai_agents.tools.gitlab_tools import GitLabSHATool, GitLabFileTool, GitLabTimestampTool
        
        gitlab_config = self.config.get("gitlab", {})
        
        # Create tool instances
        sha_tool = GitLabSHATool({"gitlab": gitlab_config})
        content_tool = GitLabFileTool({"gitlab": gitlab_config})
        timestamp_tool = GitLabTimestampTool({"gitlab": gitlab_config})
        
        def get_file_sha_wrapper(input_str: str) -> str:
            """Wrapper for GitLab SHA tool"""
            try:
                params = json.loads(input_str)
                result = sha_tool.run(params)
                return json.dumps(result, indent=2)
            except json.JSONDecodeError as e:
                return f"Error parsing input: {str(e)}. Expected JSON with 'commit_ref' and 'file_path' keys."
            except Exception as e:
                return f"Error: {str(e)}"

        def get_file_content_wrapper(input_str: str) -> str:
            """Wrapper for GitLab content tool"""
            try:
                params = json.loads(input_str)
                result = content_tool.run(params)
                return json.dumps(result, indent=2)
            except json.JSONDecodeError as e:
                return f"Error parsing input: {str(e)}. Expected JSON with 'file_sha' key."
            except Exception as e:
                return f"Error: {str(e)}"

        def get_file_timestamp_wrapper(input_str: str) -> str:
            """Wrapper for GitLab timestamp tool"""
            try:
                params = json.loads(input_str)
                result = timestamp_tool.run(params)
                return json.dumps(result, indent=2)
            except json.JSONDecodeError as e:
                return f"Error parsing input: {str(e)}. Expected JSON with 'branch', 'timestamp', and 'file_path' keys."
            except Exception as e:
                return f"Error: {str(e)}"

        def extract_info_wrapper(input_str: str) -> str:
            """Extract structured information from user query"""
            try:
                extracted = self._extract_info_from_query(input_str)
                return json.dumps(extracted, indent=2)
            except Exception as e:
                return f"Error extracting info: {str(e)}"

        return [
            Tool(
                name="get_file_sha",
                description="Get SHA hash of a file at a specific commit reference. Input should be JSON with 'commit_ref' and 'file_path' keys.",
                func=get_file_sha_wrapper
            ),
            Tool(
                name="get_file_sha_from_branch_timestamp",
                description="Get SHA hash of a file from latest commit on branch before a specific timestamp. Input should be JSON with 'branch', 'timestamp', and 'file_path' keys.",
                func=get_file_timestamp_wrapper
            ),
            Tool(
                name="get_file_content",
                description="Get file content using SHA hash. Input should be JSON with 'file_sha' key.",
                func=get_file_content_wrapper
            ),
            Tool(
                name="extract_info",
                description="Extract structured information (file paths, branches, timestamps, etc.) from user query. Input should be the user query string.",
                func=extract_info_wrapper
            )
        ]

    def _create_file_tools(self) -> List[Tool]:
        """Create file system tools."""
        from ai_agents.tools.file_tools import PCAPListTool, LogListTool, PCAPInfoTool, LogContentTool, LogSearchTool
        
        file_config = {
            "pcap_folder": self.config.get("file_system", {}).get("pcap_folder", "pcap"),
            "log_folder": self.config.get("file_system", {}).get("log_folder", "logs")
        }
        
        # Create tool instances
        pcap_list_tool = PCAPListTool(file_config)
        log_list_tool = LogListTool(file_config)
        pcap_info_tool = PCAPInfoTool(file_config)
        log_content_tool = LogContentTool(file_config)
        log_search_tool = LogSearchTool(file_config)
        
        def list_pcap_wrapper(input_str: str = "*.pcap") -> str:
            """Wrapper for PCAP list tool"""
            try:
                pattern = input_str.strip().strip("'\"") if input_str.strip() else "*.pcap"
                result = pcap_list_tool.run({"pattern": pattern})
                return json.dumps(result, indent=2)
            except Exception as e:
                return f"Error listing PCAP files: {str(e)}"

        def list_log_wrapper(input_str: str = "*.log") -> str:
            """Wrapper for log list tool"""
            try:
                pattern = input_str.strip().strip("'\"") if input_str.strip() else "*.log"
                result = log_list_tool.run({"pattern": pattern})
                return json.dumps(result, indent=2)
            except Exception as e:
                return f"Error listing log files: {str(e)}"

        def get_pcap_info_wrapper(input_str: str) -> str:
            """Wrapper for PCAP info tool"""
            try:
                filename = input_str.strip().strip("'\"")
                if not filename:
                    return "Error: Filename cannot be empty"
                result = pcap_info_tool.run({"filename": filename})
                return json.dumps(result, indent=2)
            except Exception as e:
                return f"Error getting PCAP info: {str(e)}"

        def get_log_content_wrapper(input_str: str) -> str:
            """Wrapper for log content tool"""
            try:
                if input_str.startswith('{'):
                    params = json.loads(input_str)
                    filename = params['filename'].strip().strip("'\"")
                    max_lines = params.get('max_lines', 1000)
                    tail = params.get('tail', True)
                else:
                    filename = input_str.strip().strip("'\"")
                    max_lines = 1000
                    tail = True

                if not filename:
                    return "Error: Filename cannot be empty"
                
                result = log_content_tool.run({
                    "filename": filename,
                    "max_lines": max_lines,
                    "tail": tail
                })
                return json.dumps(result, indent=2)
            except json.JSONDecodeError as e:
                return f"Error parsing input: {str(e)}. Expected JSON with 'filename' key or just filename string."
            except Exception as e:
                return f"Error getting log content: {str(e)}"

        def search_logs_wrapper(input_str: str) -> str:
            """Wrapper for log search tool"""
            try:
                if input_str.startswith('{'):
                    params = json.loads(input_str)
                    search_term = params['search_term'].strip().strip("'\"")
                    max_results = params.get('max_results', 100)
                else:
                    search_term = input_str.strip().strip("'\"")
                    max_results = 100

                if not search_term:
                    return "Error: Search term cannot be empty"
                
                result = log_search_tool.run({
                    "search_term": search_term,
                    "max_results": max_results
                })
                return json.dumps(result, indent=2)
            except json.JSONDecodeError as e:
                return f"Error parsing input: {str(e)}. Expected JSON with 'search_term' key or just search term string."
            except Exception as e:
                return f"Error searching logs: {str(e)}"

        return [
            Tool(
                name="list_pcap_files",
                description="List PCAP files in the configured folder. Input should be a file pattern (e.g., '*.pcap', '*.pcapng').",
                func=list_pcap_wrapper
            ),
            Tool(
                name="list_log_files",
                description="List log files in the configured folder. Input should be a file pattern (e.g., '*.log', '*.txt').",
                func=list_log_wrapper
            ),
            Tool(
                name="get_pcap_info",
                description="Get detailed information about a specific PCAP file. Input should be the filename.",
                func=get_pcap_info_wrapper
            ),
            Tool(
                name="get_log_content",
                description="Get content from a log file. Input should be JSON with 'filename' and optional 'max_lines', 'tail' keys, or just the filename.",
                func=get_log_content_wrapper
            ),
            Tool(
                name="search_logs",
                description="Search for a term across all log files. Input should be JSON with 'search_term' and optional 'max_results' keys, or just the search term.",
                func=search_logs_wrapper
            )
        ]

    def _create_sql_tools(self) -> List[Tool]:
        """Create SQL database tools."""
        if not self.db:
            return []

        try:
            sql_toolkit = SQLDatabaseToolkit(db=self.db, llm=self.sql_model)
            sql_tools = sql_toolkit.get_tools()

            # Create wrapped SQL tools to handle quoted inputs
            wrapped_sql_tools = []
            for tool in sql_tools:
                if tool.name == "sql_db_schema":
                    original_tool = tool
                    def schema_wrapper(input_str: str, orig_tool=original_tool) -> str:
                        """Wrapper for sql_db_schema that handles quoted table names"""
                        try:
                            cleaned_input = input_str.strip().strip("'\"")
                            return orig_tool._run(cleaned_input)
                        except Exception as e:
                            return f"Error getting schema: {str(e)}"

                    wrapped_tool = Tool(
                        name=tool.name,
                        description=tool.description,
                        func=schema_wrapper
                    )
                    wrapped_sql_tools.append(wrapped_tool)
                elif tool.name == "sql_db_query":
                    original_tool = tool
                    def query_wrapper(input_str: str, orig_tool=original_tool) -> str:
                        """Wrapper for sql_db_query that handles quoted queries"""
                        try:
                            cleaned_input = input_str.strip()
                            if (cleaned_input.startswith('"') and cleaned_input.endswith('"')) or \
                               (cleaned_input.startswith("'") and cleaned_input.endswith("'")):
                                cleaned_input = cleaned_input[1:-1]
                            return orig_tool._run(cleaned_input)
                        except Exception as e:
                            return f"Error executing query: {str(e)}"

                    wrapped_tool = Tool(
                        name=tool.name,
                        description=tool.description,
                        func=query_wrapper
                    )
                    wrapped_sql_tools.append(wrapped_tool)
                elif tool.name == "sql_db_query_checker":
                    original_tool = tool
                    def query_checker_wrapper(input_str: str, orig_tool=original_tool) -> str:
                        """Wrapper for sql_db_query_checker that handles quoted queries"""
                        try:
                            cleaned_input = input_str.strip()
                            if (cleaned_input.startswith('"') and cleaned_input.endswith('"')) or \
                               (cleaned_input.startswith("'") and cleaned_input.endswith("'")):
                                cleaned_input = cleaned_input[1:-1]
                            return orig_tool._run(cleaned_input)
                        except Exception as e:
                            return f"Error checking query: {str(e)}"

                    wrapped_tool = Tool(
                        name=tool.name,
                        description=tool.description,
                        func=query_checker_wrapper
                    )
                    wrapped_sql_tools.append(wrapped_tool)
                else:
                    wrapped_sql_tools.append(tool)

            return wrapped_sql_tools
        except Exception as e:
            print(f"Warning: Could not create SQL tools: {e}")
            return []

    def _create_utility_tools(self) -> List[Tool]:
        """Create utility tools like final answer tool."""
        def final_answer_tool_func(input: Union[str, List]) -> str:
            """Tool to format final output, handling list or string input"""
            if isinstance(input, list):
                output_lines = []
                for row in input:
                    line = " | ".join(str(cell) for cell in row)
                    output_lines.append(line)
                output = "\n".join(output_lines)
            elif isinstance(input, str):
                cleaned = []
                for line in input.strip().splitlines():
                    line = line.strip()
                    if line.startswith("|") and "|" in line:
                        parts = [cell.strip() for cell in line.split("|") if cell.strip()]
                        if parts:
                            cleaned.append(" | ".join(parts))
                    elif line:
                        cleaned.append(line)
                output = "\n".join(cleaned)
            else:
                output = str(input)

            return output

        return [
            Tool(
                name="final_answer_tool",
                func=final_answer_tool_func,
                description="Use this tool to provide the final answer to the user and stop.",
                return_direct=True
            )
        ]

    def build_agent(self) -> AgentExecutor:
        """Build the ReAct agent executor."""
        react_prompt = PromptTemplate.from_template("""
You are a unified assistant that can help with GitLab file operations, SQL database queries, and local file system operations (PCAP and log files).

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

IMPORTANT RULES:

For GitLab operations:
1. Always start by using the 'extract_info' tool to understand what the user is asking for
2. For getting file content, you typically need to:
   - First get the SHA hash using 'get_file_sha' or 'get_file_sha_from_branch_timestamp'
   - Then get the content using 'get_file_content'
3. Handle timestamps by normalizing them to ISO format (YYYY-MM-DDTHH:MM:SSZ)
4. Default branch is 'main' if not specified

For SQL operations:
1. Never make up or guess any value (e.g., `sha`, `status`, `duration`, etc.).
2. Start by using `sql_db_schema` to understand the database structure
3. Use `sql_db_query_checker` to verify any SQL query before running it.
4. Use `sql_db_query` to retrieve real data.
5. Use JOINs between tables only when needed and based on schema relationships.
6. When finished with SQL queries, use the `final_answer_tool` with the exact result.
7. Do NOT format SQL code using markdown or triple backticks.

For File System operations (PCAP and Log files):
1. Use `list_pcap_files` to see available PCAP files in the pcap folder
2. Use `list_log_files` to see available log files in the log folder
3. Use `get_pcap_info` to get detailed information about a specific PCAP file
4. Use `get_log_content` to read log file content (supports limiting lines and tail/head options)
5. Use `search_logs` to search for specific terms across all log files
6. File operations work with local file system, not GitLab repository

General rules:
7. If a tool returns an error, DO NOT retry the same action. Instead, explain the error in your Final Answer
8. Once you have the information requested, immediately provide the Final Answer - do not keep searching
9. If you cannot complete the task due to errors, explain what went wrong in your Final Answer

Begin!

Question: {input}
Thought: {agent_scratchpad}
""")

        agent = create_react_agent(self.model, self.tools, react_prompt)

        agent_config = self.config.get("agent", {})
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=agent_config.get("verbose", True),
            handle_parsing_errors=True,
            max_iterations=agent_config.get("max_iterations", 15),
            max_execution_time=agent_config.get("execution_timeout", 300.0),
            early_stopping_method="force",
            return_intermediate_steps=True
        )

    def build_clean_agent(self) -> AgentExecutor:
        """Build the ReAct agent executor with clean output (no verbose logging)."""
        react_prompt = PromptTemplate.from_template("""
You are a unified assistant that can help with GitLab file operations, SQL database queries, and local file system operations (PCAP and log files).

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

IMPORTANT RULES:

For GitLab operations:
1. Always start by using the 'extract_info' tool to understand what the user is asking for
2. For getting file content, you typically need to:
   - First get the SHA hash using 'get_file_sha' or 'get_file_sha_from_branch_timestamp'
   - Then get the content using 'get_file_content'
3. Handle timestamps by normalizing them to ISO format (YYYY-MM-DDTHH:MM:SSZ)
4. Default branch is 'main' if not specified

For SQL operations:
1. Never make up or guess any value (e.g., `sha`, `status`, `duration`, etc.).
2. Start by using `sql_db_schema` to understand the database structure
3. Use `sql_db_query_checker` to verify any SQL query before running it.
4. Use `sql_db_query` to retrieve real data.
5. Use JOINs between tables only when needed and based on schema relationships.
6. When finished with SQL queries, use the `final_answer_tool` with the exact result.
7. Do NOT format SQL code using markdown or triple backticks.

For File System operations (PCAP and Log files):
1. Use `list_pcap_files` to see available PCAP files in the pcap folder
2. Use `list_log_files` to see available log files in the log folder
3. Use `get_pcap_info` to get detailed information about a specific PCAP file
4. Use `get_log_content` to read log file content (supports limiting lines and tail/head options)
5. Use `search_logs` to search for specific terms across all log files
6. File operations work with local file system, not GitLab repository

General rules:
7. If a tool returns an error, DO NOT retry the same action. Instead, explain the error in your Final Answer
8. Once you have the information requested, immediately provide the Final Answer - do not keep searching
9. If you cannot complete the task due to errors, explain what went wrong in your Final Answer

Begin!

Question: {input}
Thought: {agent_scratchpad}
""")

        agent = create_react_agent(self.model, self.tools, react_prompt)

        agent_config = self.config.get("agent", {})
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=False,  # Disable verbose output for clean mode
            handle_parsing_errors=True,
            max_iterations=agent_config.get("max_iterations", 15),
            max_execution_time=agent_config.get("execution_timeout", 300.0),
            early_stopping_method="force",
            return_intermediate_steps=False  # Don't return intermediate steps for clean output
        )

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run the unified ReAct agent.

        Args:
            input_data: Dict containing the user query

        Returns:
            Dict with the agent's response and metadata
        """
        if not isinstance(input_data, dict):
            raise ValueError("Input data must be a dictionary")

        query = input_data.get("query") or input_data.get("input")
        if not query:
            raise ValueError("Input must contain a 'query' or 'input' key")

        try:
            result = self.agent.invoke({"input": query})

            return {
                "query": query,
                "response": result["output"],
                "intermediate_steps": result.get("intermediate_steps", []),
                "metadata": {
                    "tools_available": [tool.name for tool in self.tools],
                    "model": getattr(self.model, 'model', 'unknown'),
                    "sql_enabled": self.db is not None,
                    "num_iterations": len(result.get("intermediate_steps", [])),
                    "status": "success"
                }
            }
        except Exception as e:
            return {
                "query": query,
                "response": f"Error processing query: {str(e)}",
                "error": str(e),
                "metadata": {
                    "status": "error"
                }
            }

    def process_query(self, user_input: str) -> str:
        """Process a user query using the ReAct agent with clean output formatting.

        This method provides the same clean output format as the original v1_agent.

        Args:
            user_input: The user's query string

        Returns:
            str: Clean response string without verbose metadata
        """
        try:
            # Initialize clean agent if not already done
            if self.clean_agent is None:
                self.clean_agent = self.build_clean_agent()

            result = self.clean_agent.invoke({"input": user_input})

            # Check if the agent hit the iteration limit
            if "intermediate_steps" in result:
                if len(result["intermediate_steps"]) >= self.config.get("agent", {}).get("max_iterations", 15):
                    return f"The agent reached the maximum number of iterations. Last output: {result.get('output', 'No output available')}"

            return result["output"]
        except Exception as e:
            return f"Error processing query: {str(e)}"

    def process_query_debug(self, user_input: str) -> Dict[str, Any]:
        """Process a user query with detailed debugging information.

        Args:
            user_input: The user's query string

        Returns:
            Dict with detailed debugging information
        """
        try:
            result = self.agent.invoke({"input": user_input})

            debug_info = {
                "output": result["output"],
                "intermediate_steps": result.get("intermediate_steps", []),
                "num_iterations": len(result.get("intermediate_steps", [])),
                "hit_limit": len(result.get("intermediate_steps", [])) >= self.config.get("agent", {}).get("max_iterations", 15),
                "status": "success"
            }

            return debug_info
        except Exception as e:
            return {
                "output": f"Error processing query: {str(e)}",
                "intermediate_steps": [],
                "num_iterations": 0,
                "hit_limit": False,
                "status": "error",
                "error": str(e)
            }

    def process_queries_batch(self, queries: List[str]) -> List[Dict[str, str]]:
        """Process multiple queries and return results.

        Args:
            queries: List of query strings

        Returns:
            List of result dictionaries
        """
        results = []
        for query in queries:
            try:
                response = self.process_query(query)
                results.append({
                    "query": query,
                    "response": response,
                    "status": "success"
                })
            except Exception as e:
                results.append({
                    "query": query,
                    "response": f"Error: {str(e)}",
                    "status": "error"
                })
        return results

    def _extract_info_from_query(self, query: str) -> Dict[str, Any]:
        """Extract information from user query using regex patterns"""
        patterns = {
            'sha': r'([a-f0-9]{40}|[a-f0-9]{7,})',  # SHA hash (full or short)
            'branch': r'(?:branch|on|from)\s+([a-zA-Z0-9_\-\/]+)',
            'timestamp': r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z?|\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4})',
            'file_path': r'(?:file|path)\s+([a-zA-Z0-9_\-\/\.]+)|([a-zA-Z0-9_\-\/\.]+\.[a-zA-Z]+)',
            'commit_ref': r'(?:commit|ref)\s+([a-zA-Z0-9_\-\/]+)',
        }

        extracted = {}

        for key, pattern in patterns.items():
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                extracted[key] = match.group(1) if match.group(1) else match.group(2)

        # Try to infer file path from common extensions if not explicitly found
        if 'file_path' not in extracted:
            file_extensions = r'([a-zA-Z0-9_\-\/]+\.(?:py|js|json|md|txt|yml|yaml|xml|html|css|java|cpp|c|h))'
            match = re.search(file_extensions, query, re.IGNORECASE)
            if match:
                extracted['file_path'] = match.group(1)

        # Determine intent
        query_lower = query.lower()
        if any(word in query_lower for word in ['content', 'show', 'read', 'display', 'what', 'contains']):
            extracted['intent'] = 'get_content'
        elif any(word in query_lower for word in ['sha', 'hash', 'id']):
            extracted['intent'] = 'get_sha'
        elif 'file_path' in extracted:
            extracted['intent'] = 'get_content'  # Default to content if file path found
        else:
            extracted['intent'] = 'unknown'

        return extracted
