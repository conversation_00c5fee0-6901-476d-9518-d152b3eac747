# Unified ReAct Agent Configuration
# This agent combines GitLab file operations, SQL database queries, and local file system operations

# Main model configuration (for GitLab and general operations)
model:
  type: mistral
  model_id: mistral-medium
  config:
    temperature: 0.0
    max_tokens: 1000
    api_url: https://api.mistral.ai/v1/chat/completions

# SQL model configuration (optimized for SQL operations)
sql_model:
  type: mistral-chat
  model_id: mistral-large-latest
  config:
    temperature: 0.0
    max_tokens: 1000
    api_url: https://api.mistral.ai/v1/chat/completions

# GitLab configuration
gitlab:
  url: https://gitlab.com
  project_id: "71217006"
  # token: will be loaded from environment variable GITLAB_TOKEN

# SQL database configuration
sql:
  enabled: true
  server: localhost
  database: STAGE
  use_trusted_connection: true
  # username: # only needed if use_trusted_connection is false
  # password: # only needed if use_trusted_connection is false
  tables:
    - execution
    - testcase
    - run
  dialect: mssql

# File system configuration
file_system:
  pcap_folder: pcap
  log_folder: logs
  max_log_lines: 1000
  max_search_results: 100
  pcap_patterns:
    - "*.pcap"
    - "*.pcapng"
    - "*.cap"
  log_patterns:
    - "*.log"
    - "*.txt"
    - "*.out"
    - "*.err"

# Agent behavior configuration
agent:
  verbose: true
  max_iterations: 15
  execution_timeout: 300.0
  debug: false

# Memory configuration (optional)
memory:
  type: vector
  config:
    embedding_model: openai
    store_path: ./vector_store_unified

# Logging configuration
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  # file: agent.log  # optional log file
