#!/usr/bin/env python3
"""
Clean CLI for the Unified ReAct Agent.

This provides a clean interface similar to the original v1_agent,
without verbose framework output.
"""

import sys
import os
from pathlib import Path

# Add src to Python path if needed
if str(Path(__file__).resolve().parent.parent.parent) not in sys.path:
    sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

from ai_agents.agents.registry import get_agent
from ai_agents.utils.config_loader import load_env_vars


def main():
    """Main CLI entry point with clean output."""
    print("🤖 Unified ReAct Agent - Interactive Mode")
    print("=" * 60)
    print("This agent can help with:")
    print("• GitLab file operations (get content, SHAs, historical files)")
    print("• SQL database queries (schema, data retrieval)")
    print("• Local file operations (PCAP files, log files)")
    print("=" * 60)
    
    # Load environment variables
    env_vars = load_env_vars()
    if not env_vars.get("MISTRAL_API_KEY"):
        print("❌ Error: MISTRAL_API_KEY environment variable is required")
        print("Please set it in your .env file")
        sys.exit(1)
    
    try:
        # Create the agent
        agent = get_agent("unified_react")
        print(f"✅ Agent initialized successfully!")
        print(f"   Tools available: {len(agent.tools)}")
        print(f"   SQL enabled: {agent.db is not None}")
        print()
        
    except Exception as e:
        print(f"❌ Error initializing agent: {str(e)}")
        sys.exit(1)
    
    # Example queries
    example_queries = [
        "List all tables in the database",
        "Show me the schema of the testcase table",
        "List all PCAP files",
        "Search for 'error' in log files"
    ]
    
    print("💡 Example queries you can try:")
    for i, query in enumerate(example_queries, 1):
        print(f"   {i}. {query}")
    print()
    
    # Interactive mode
    print("Type your query (or 'quit' to exit):")
    print("-" * 60)
    
    while True:
        try:
            user_input = input("\n🔍 Query: ")
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not user_input.strip():
                continue
            
            print("\n🤔 Processing...")
            
            # Use the clean process_query method
            response = agent.process_query(user_input)
            
            print(f"\n📋 Response:")
            print("-" * 40)
            print(response)
            print("-" * 40)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")


def run_single_query(query: str):
    """Run a single query and return clean output."""
    try:
        # Load environment variables
        env_vars = load_env_vars()
        if not env_vars.get("MISTRAL_API_KEY"):
            return "❌ Error: MISTRAL_API_KEY environment variable is required"
        
        # Create the agent
        agent = get_agent("unified_react")
        
        # Process the query
        response = agent.process_query(query)
        return response
        
    except Exception as e:
        return f"❌ Error: {str(e)}"


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Single query mode
        query = " ".join(sys.argv[1:])
        response = run_single_query(query)
        print(response)
    else:
        # Interactive mode
        main()
