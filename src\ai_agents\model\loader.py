"""ModelLoader is a utility class for loading language models with configuration.

It supports different model types and configurations, allowing for flexible model initialization.
"""

from typing import Any, Dict, Optional, List
from langchain_openai import ChatOpenAI
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.language_models.llms import LLM
from langchain_core.language_models.chat_models import SimpleChatModel
from langchain_core.callbacks.manager import CallbackManagerForLLMRun
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel, Field
import requests


class MistralLLM(LLM):
    """Custom LangChain LLM wrapper for Mistral API (completion mode)"""

    api_key: str = Field(...)
    model: str = Field(default="mistral-medium")
    temperature: float = Field(default=0.0)
    max_tokens: int = Field(default=1000)
    api_url: str = Field(default="https://api.mistral.ai/v1/chat/completions")

    def __init__(self, api_key: str, model: str = "mistral-medium", **kwargs):
        super().__init__(api_key=api_key, model=model, **kwargs)

    @property
    def _llm_type(self) -> str:
        return "mistral"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """Make a request to Mistral API"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        messages = [{"role": "user", "content": prompt}]

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

        if stop:
            data["stop"] = stop

        try:
            response = requests.post(self.api_url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            return f"Error calling Mistral API: {str(e)}"


class MistralChatModel(SimpleChatModel, BaseModel):
    """Custom Mistral LLM wrapper with chat functionality for SQL operations"""
    api_key: str = Field(...)
    model: str = Field(default="mistral-large-latest")
    api_url: str = Field(default="https://api.mistral.ai/v1/chat/completions")

    def __init__(self, api_key: str, model: str = "mistral-large-latest", **kwargs):
        super().__init__(api_key=api_key, model=model, **kwargs)

    def _call(self, messages: List[HumanMessage], stop: Optional[List[str]] = None, **kwargs: Any) -> str:
        # Filter out LangChain-specific parameters that Mistral doesn't understand
        filtered_kwargs = {}
        for key, value in kwargs.items():
            if key not in ['run_manager', 'callbacks']:
                filtered_kwargs[key] = value

        filtered_kwargs["temperature"] = 0

        converted = []
        for m in messages:
            if isinstance(m, HumanMessage):
                converted.append({"role": "user", "content": m.content})
            elif isinstance(m, AIMessage):
                converted.append({"role": "assistant", "content": m.content})

        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            data = {
                "model": self.model,
                "messages": converted,
                **filtered_kwargs
            }

            response = requests.post(self.api_url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            return f"Error calling Mistral API: {str(e)}"

    @property
    def _llm_type(self) -> str:
        return "mistral-chat"


class ModelLoader:
    """Utility class for loading language models with configuration."""

    @staticmethod
    def load_model(
        model_type: str, model_id: str, config: Optional[Dict[str, Any]] = None
    ) -> BaseLanguageModel:
        """
        Load a language model with the given configuration.

        Args:
            model_type (str): Type of model ("openai", "mistral", "mistral-chat", etc.)
            model_id (str): Model identifier
            config (dict, optional): Model-specific configuration

        Returns:
            BaseLanguageModel: Configured language model

        Raises:
            ValueError: If model type is not supported
        """
        config = config or {}
        model_type = model_type.lower()

        if model_type == "openai":
            # Support for custom OpenAI API base
            openai_api_base = config.get("openai_api_base")
            kwargs = {
                "model": model_id,
                "temperature": config.get("temperature", 0.7),
                "max_tokens": config.get("max_tokens", 1000),
            }
            if openai_api_base:
                kwargs["base_url"] = openai_api_base
            return ChatOpenAI(**kwargs)
        elif model_type == "mistral":
            # Mistral LLM for completion mode
            api_key = config.get("api_key")
            if not api_key:
                raise ValueError("Mistral API key is required")

            kwargs = {
                "api_key": api_key,
                "model": model_id,
                "temperature": config.get("temperature", 0.0),
                "max_tokens": config.get("max_tokens", 1000),
            }

            api_url = config.get("api_url")
            if api_url:
                kwargs["api_url"] = api_url

            return MistralLLM(**kwargs)
        elif model_type == "mistral-chat":
            # Mistral Chat Model for chat mode
            api_key = config.get("api_key")
            if not api_key:
                raise ValueError("Mistral API key is required")

            kwargs = {
                "api_key": api_key,
                "model": model_id,
            }

            api_url = config.get("api_url")
            if api_url:
                kwargs["api_url"] = api_url

            return MistralChatModel(**kwargs)
        else:
            raise ValueError(f"Unsupported model type: {model_type}")
