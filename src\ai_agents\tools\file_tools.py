"""Local file system tools for PCAP and log file operations.

This module provides tools for retrieving and analyzing PCAP files and log files
from local directories.
"""

import os
import glob
import json
import mimetypes
from datetime import datetime
from typing import Dict, Any, List
from .base import BaseTool


class FileSystemError(Exception):
    """Custom exception for file system errors"""
    pass


class PCAPListTool(BaseTool):
    """Tool for listing PCAP files in a configured folder."""

    @property
    def name(self) -> str:
        return "pcap_list"

    @property
    def description(self) -> str:
        return "List PCAP files in the configured folder. Input should be a file pattern (e.g., '*.pcap', '*.pcapng')."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """List PCAP files in the configured folder.
        
        Args:
            input_data: Dict with optional 'pattern' key (default: "*.pcap")
                
        Returns:
            Dict with 'files' key containing list of file information
        """
        try:
            pattern = input_data.get('pattern', '*.pcap')
            pcap_folder = self.config.get('pcap_folder', 'pcap')
            
            if not os.path.exists(pcap_folder):
                return {"error": f"PCAP folder '{pcap_folder}' does not exist"}

            if not os.path.isdir(pcap_folder):
                return {"error": f"'{pcap_folder}' is not a directory"}

            # Get all PCAP files matching the pattern
            pcap_files = []

            if pattern == "*.pcap":
                # Default behavior: search for all common PCAP extensions
                pcap_patterns = ["*.pcap", "*.pcapng", "*.cap"]
                for pcap_pattern in pcap_patterns:
                    search_pattern = os.path.join(pcap_folder, pcap_pattern)
                    pcap_files.extend(glob.glob(search_pattern))
            else:
                # Use the specific pattern provided
                search_pattern = os.path.join(pcap_folder, pattern)
                pcap_files = glob.glob(search_pattern)

            file_list = []
            for file_path in pcap_files:
                try:
                    stat_info = os.stat(file_path)
                    file_info = {
                        "filename": os.path.basename(file_path),
                        "full_path": os.path.abspath(file_path),
                        "size_bytes": stat_info.st_size,
                        "size_mb": round(stat_info.st_size / (1024 * 1024), 2),
                        "created_time": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                        "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                        "extension": os.path.splitext(file_path)[1].lower()
                    }
                    file_list.append(file_info)
                except OSError as e:
                    continue

            # Sort by modification time (newest first)
            file_list.sort(key=lambda x: x["modified_time"], reverse=True)

            return {"files": file_list, "count": len(file_list), "folder": pcap_folder}

        except Exception as e:
            return {"error": f"Failed to list PCAP files: {str(e)}"}


class LogListTool(BaseTool):
    """Tool for listing log files in a configured folder."""

    @property
    def name(self) -> str:
        return "log_list"

    @property
    def description(self) -> str:
        return "List log files in the configured folder. Input should be a file pattern (e.g., '*.log', '*.txt')."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """List log files in the configured folder.
        
        Args:
            input_data: Dict with optional 'pattern' key (default: "*.log")
                
        Returns:
            Dict with 'files' key containing list of file information
        """
        try:
            pattern = input_data.get('pattern', '*.log')
            log_folder = self.config.get('log_folder', 'logs')
            
            if not os.path.exists(log_folder):
                return {"error": f"Log folder '{log_folder}' does not exist"}
            
            if not os.path.isdir(log_folder):
                return {"error": f"'{log_folder}' is not a directory"}
            
            # Get all log files matching the pattern
            search_pattern = os.path.join(log_folder, pattern)
            log_files = glob.glob(search_pattern)
            
            # Also check for common log extensions
            additional_patterns = ["*.txt", "*.out", "*.err"]
            for add_pattern in additional_patterns:
                additional_search = os.path.join(log_folder, add_pattern)
                log_files.extend(glob.glob(additional_search))
            
            # Remove duplicates
            log_files = list(set(log_files))
            
            file_list = []
            for file_path in log_files:
                try:
                    stat_info = os.stat(file_path)
                    file_info = {
                        "filename": os.path.basename(file_path),
                        "full_path": os.path.abspath(file_path),
                        "size_bytes": stat_info.st_size,
                        "size_kb": round(stat_info.st_size / 1024, 2),
                        "created_time": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                        "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                        "extension": os.path.splitext(file_path)[1].lower()
                    }
                    file_list.append(file_info)
                except OSError as e:
                    continue
            
            # Sort by modification time (newest first)
            file_list.sort(key=lambda x: x["modified_time"], reverse=True)
            
            return {"files": file_list, "count": len(file_list), "folder": log_folder}
            
        except Exception as e:
            return {"error": f"Failed to list log files: {str(e)}"}


class PCAPInfoTool(BaseTool):
    """Tool for getting detailed information about a specific PCAP file."""

    @property
    def name(self) -> str:
        return "pcap_info"

    @property
    def description(self) -> str:
        return "Get detailed information about a specific PCAP file. Input should be the filename."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get detailed information about a PCAP file.
        
        Args:
            input_data: Dict with 'filename' key
                
        Returns:
            Dict with detailed file information
        """
        try:
            filename = input_data['filename']
            pcap_folder = self.config.get('pcap_folder', 'pcap')
            
            file_path = os.path.join(pcap_folder, filename)
            
            if not os.path.exists(file_path):
                return {"error": f"PCAP file '{filename}' not found in '{pcap_folder}'"}
            
            if not os.path.isfile(file_path):
                return {"error": f"'{filename}' is not a file"}
            
            stat_info = os.stat(file_path)
            
            file_info = {
                "filename": filename,
                "full_path": os.path.abspath(file_path),
                "size_bytes": stat_info.st_size,
                "size_mb": round(stat_info.st_size / (1024 * 1024), 2),
                "created_time": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                "accessed_time": datetime.fromtimestamp(stat_info.st_atime).isoformat(),
                "extension": os.path.splitext(file_path)[1].lower(),
                "mime_type": mimetypes.guess_type(file_path)[0],
                "is_readable": os.access(file_path, os.R_OK),
                "folder": pcap_folder
            }
            
            return file_info
            
        except Exception as e:
            return {"error": f"Failed to get PCAP file info: {str(e)}"}


class LogContentTool(BaseTool):
    """Tool for getting content from a log file."""

    @property
    def name(self) -> str:
        return "log_content"

    @property
    def description(self) -> str:
        return "Get content from a log file. Input should be JSON with 'filename' and optional 'max_lines', 'tail' keys."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get content from a log file.
        
        Args:
            input_data: Dict with keys:
                - filename: Name of the log file
                - max_lines: Maximum number of lines to return (default: 1000)
                - tail: If True, return last N lines; if False, return first N lines (default: True)
                
        Returns:
            Dict with file content and metadata
        """
        try:
            filename = input_data['filename']
            max_lines = input_data.get('max_lines', 1000)
            tail = input_data.get('tail', True)
            log_folder = self.config.get('log_folder', 'logs')
            
            file_path = os.path.join(log_folder, filename)
            
            if not os.path.exists(file_path):
                return {"error": f"Log file '{filename}' not found in '{log_folder}'"}
            
            if not os.path.isfile(file_path):
                return {"error": f"'{filename}' is not a file"}
            
            if not os.access(file_path, os.R_OK):
                return {"error": f"No read permission for '{filename}'"}
            
            # Get file info
            stat_info = os.stat(file_path)
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            total_lines = len(lines)
            
            # Apply line limiting
            if total_lines > max_lines:
                if tail:
                    content_lines = lines[-max_lines:]
                    truncated_info = f"Showing last {max_lines} lines of {total_lines} total lines"
                else:
                    content_lines = lines[:max_lines]
                    truncated_info = f"Showing first {max_lines} lines of {total_lines} total lines"
            else:
                content_lines = lines
                truncated_info = f"Showing all {total_lines} lines"
            
            content = ''.join(content_lines)
            
            return {
                "filename": filename,
                "full_path": os.path.abspath(file_path),
                "content": content,
                "total_lines": total_lines,
                "returned_lines": len(content_lines),
                "truncated": total_lines > max_lines,
                "truncated_info": truncated_info,
                "size_bytes": stat_info.st_size,
                "size_kb": round(stat_info.st_size / 1024, 2),
                "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                "encoding": "utf-8"
            }
            
        except UnicodeDecodeError:
            return {"error": f"Cannot decode '{input_data.get('filename')}' as text file"}
        except Exception as e:
            return {"error": f"Failed to read log file: {str(e)}"}


class LogSearchTool(BaseTool):
    """Tool for searching across all log files."""

    @property
    def name(self) -> str:
        return "log_search"

    @property
    def description(self) -> str:
        return "Search for a term across all log files. Input should be JSON with 'search_term' and optional 'max_results' keys."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Search for a term across all log files.
        
        Args:
            input_data: Dict with keys:
                - search_term: Term to search for
                - max_results: Maximum number of matching lines to return (default: 100)
                
        Returns:
            Dict with search results
        """
        try:
            search_term = input_data['search_term']
            max_results = input_data.get('max_results', 100)
            log_folder = self.config.get('log_folder', 'logs')
            
            if not search_term.strip():
                return {"error": "Search term cannot be empty"}
            
            # Get list of log files
            log_list_tool = LogListTool(self.config)
            log_files_result = log_list_tool.run({})
            
            if 'error' in log_files_result:
                return log_files_result
                
            log_files = log_files_result['files']
            results = []
            
            for file_info in log_files:
                try:
                    with open(file_info["full_path"], 'r', encoding='utf-8', errors='ignore') as f:
                        for line_num, line in enumerate(f, 1):
                            if search_term.lower() in line.lower():
                                results.append({
                                    "filename": file_info["filename"],
                                    "line_number": line_num,
                                    "line_content": line.strip(),
                                    "file_path": file_info["full_path"],
                                    "file_modified": file_info["modified_time"]
                                })
                                
                                if len(results) >= max_results:
                                    break
                    
                    if len(results) >= max_results:
                        break
                        
                except Exception as e:
                    continue
            
            return {
                "search_term": search_term,
                "results": results,
                "total_matches": len(results),
                "max_results": max_results,
                "truncated": len(results) >= max_results,
                "searched_folder": log_folder
            }
            
        except Exception as e:
            return {"error": f"Failed to search log files: {str(e)}"}
