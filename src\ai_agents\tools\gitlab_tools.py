"""GitLab API tools for repository file operations.

This module provides tools for interacting with GitLab repositories,
including getting file SHAs, content, and handling timestamp-based queries.
"""

import requests
import base64
import json
from datetime import datetime
from typing import Dict, Any, Optional
from .base import BaseTool


class GitLabAPIError(Exception):
    """Custom exception for GitLab API errors"""
    pass


class GitLabSHATool(BaseTool):
    """Tool for getting file SHA hash from GitLab repository."""

    @property
    def name(self) -> str:
        return "gitlab_file_sha"

    @property
    def description(self) -> str:
        return "Get SHA hash of a file at a specific commit reference. Input should be JSON with 'commit_ref' and 'file_path' keys."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get SHA hash of a file at a specific commit reference.
        
        Args:
            input_data: Dict with keys:
                - commit_ref: Commit SHA, branch name, or tag
                - file_path: Path to the file in the repository
                
        Returns:
            Dict with 'sha' key containing the file SHA hash
        """
        try:
            commit_ref = input_data.get('commit_ref', 'main')
            file_path = input_data['file_path']
            
            gitlab_config = self.config.get('gitlab', {})
            gitlab_url = gitlab_config.get('url', 'https://gitlab.com')
            project_id = gitlab_config.get('project_id')
            token = gitlab_config.get('token')
            
            if not project_id or not token:
                raise GitLabAPIError("GitLab project_id and token must be configured")
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            # URL encode the file path
            encoded_file_path = requests.utils.quote(file_path, safe='')
            
            url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/files/{encoded_file_path}"
            
            params = {'ref': commit_ref}
            
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            
            file_data = response.json()
            sha = file_data.get('blob_id')
            
            if not sha:
                raise GitLabAPIError("No blob_id found in response")
                
            return {"sha": sha, "commit_ref": commit_ref, "file_path": file_path}
            
        except requests.exceptions.HTTPError as e:
            if hasattr(e, 'response') and e.response.status_code == 404:
                return {"error": f"File '{input_data.get('file_path')}' not found at commit '{input_data.get('commit_ref', 'main')}'"}
            else:
                return {"error": f"HTTP error: {str(e)}"}
        except Exception as e:
            return {"error": f"GitLab API error: {str(e)}"}


class GitLabTimestampTool(BaseTool):
    """Tool for getting file SHA from branch at specific timestamp."""

    @property
    def name(self) -> str:
        return "gitlab_file_timestamp"

    @property
    def description(self) -> str:
        return "Get SHA hash of a file from latest commit on branch before a specific timestamp. Input should be JSON with 'branch', 'timestamp', and 'file_path' keys."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get file SHA from branch at specific timestamp.
        
        Args:
            input_data: Dict with keys:
                - branch: Branch name
                - timestamp: ISO format timestamp
                - file_path: Path to the file in the repository
                
        Returns:
            Dict with 'sha' key containing the file SHA hash
        """
        try:
            branch = input_data.get('branch', 'main')
            timestamp = input_data['timestamp']
            file_path = input_data['file_path']
            
            # Normalize timestamp
            timestamp = self._normalize_timestamp(timestamp)
            
            gitlab_config = self.config.get('gitlab', {})
            gitlab_url = gitlab_config.get('url', 'https://gitlab.com')
            project_id = gitlab_config.get('project_id')
            token = gitlab_config.get('token')
            
            if not project_id or not token:
                raise GitLabAPIError("GitLab project_id and token must be configured")
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            # Get commits from the branch before the timestamp
            commits_url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/commits"
            
            params = {
                'ref_name': branch,
                'until': timestamp,
                'per_page': 1,
                'order': 'default'
            }
            
            response = requests.get(commits_url, headers=headers, params=params)
            response.raise_for_status()
            
            commits = response.json()
            
            if not commits:
                return {"error": f"No commits found on branch '{branch}' before timestamp '{timestamp}'"}
            
            # Get the latest commit SHA
            latest_commit_sha = commits[0]['id']
            
            # Now get the file SHA from this commit using GitLabSHATool
            sha_tool = GitLabSHATool(self.config)
            result = sha_tool.run({
                'commit_ref': latest_commit_sha,
                'file_path': file_path
            })
            
            if 'error' in result:
                return result
                
            return {
                "sha": result['sha'],
                "commit_sha": latest_commit_sha,
                "branch": branch,
                "timestamp": timestamp,
                "file_path": file_path
            }
            
        except Exception as e:
            return {"error": f"GitLab timestamp query error: {str(e)}"}
    
    def _normalize_timestamp(self, timestamp: str) -> str:
        """Normalize timestamp to ISO format"""
        if '/' in timestamp:
            # MM/DD/YYYY format
            try:
                dt = datetime.strptime(timestamp, '%m/%d/%Y')
                return dt.strftime('%Y-%m-%dT00:00:00Z')
            except ValueError:
                pass
        elif 'T' not in timestamp and len(timestamp) == 10:
            # YYYY-MM-DD format
            return f"{timestamp}T00:00:00Z"
        elif not timestamp.endswith('Z'):
            # Add Z if missing
            return f"{timestamp}Z"
        
        return timestamp


class GitLabFileTool(BaseTool):
    """Tool for getting file content from GitLab using SHA hash."""

    @property
    def name(self) -> str:
        return "gitlab_file_content"

    @property
    def description(self) -> str:
        return "Get file content using SHA hash. Input should be JSON with 'file_sha' key."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get file content using SHA hash.
        
        Args:
            input_data: Dict with keys:
                - file_sha: SHA hash of the file blob
                
        Returns:
            Dict with 'content' key containing the file content
        """
        try:
            file_sha = input_data['file_sha']
            
            gitlab_config = self.config.get('gitlab', {})
            gitlab_url = gitlab_config.get('url', 'https://gitlab.com')
            project_id = gitlab_config.get('project_id')
            token = gitlab_config.get('token')
            
            if not project_id or not token:
                raise GitLabAPIError("GitLab project_id and token must be configured")
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            # Use the repository blobs API to get content by SHA
            url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/blobs/{file_sha}"
            
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            blob_data = response.json()
            
            # Content is base64 encoded, decode it
            content_b64 = blob_data.get('content', '')
            if not content_b64:
                return {"error": "No content found in blob response"}
                
            # Decode base64 content
            try:
                content = base64.b64decode(content_b64).decode('utf-8')
                return {
                    "content": content,
                    "file_sha": file_sha,
                    "size": blob_data.get('size', 0),
                    "encoding": blob_data.get('encoding', 'base64')
                }
            except Exception as e:
                return {"error": f"Cannot decode file content as UTF-8: {str(e)}"}
                
        except requests.exceptions.HTTPError as e:
            if hasattr(e, 'response') and e.response.status_code == 404:
                return {"error": f"File with SHA '{input_data.get('file_sha')}' not found"}
            else:
                return {"error": f"HTTP error: {str(e)}"}
        except Exception as e:
            return {"error": f"GitLab API error: {str(e)}"}
