"""SQL database tools for querying SQL Server databases.

This module provides tools for interacting with SQL databases,
including schema inspection, query execution, and query validation.
"""

import urllib.parse
from typing import Dict, Any, Optional
from .base import BaseTool


class SQLDatabaseError(Exception):
    """Custom exception for SQL database errors"""
    pass


class SQLSchemaTool(BaseTool):
    """Tool for getting database schema information."""

    @property
    def name(self) -> str:
        return "sql_schema"

    @property
    def description(self) -> str:
        return "Get database schema information for specified tables. Input should be table name or empty for all tables."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get database schema information.
        
        Args:
            input_data: Dict with optional 'table_name' key
                
        Returns:
            Dict with schema information
        """
        try:
            table_name = input_data.get('table_name', '')
            
            # Get database connection
            db = self._get_database_connection()
            if not db:
                return {"error": "Database connection not available"}
            
            # Use the database's get_table_info method
            if table_name:
                schema_info = db.get_table_info([table_name])
            else:
                # Get info for all configured tables
                sql_config = self.config.get('sql', {})
                tables = sql_config.get('tables', ['execution', 'testcase', 'run'])
                schema_info = db.get_table_info(tables)
            
            return {
                "schema": schema_info,
                "table_name": table_name or "all_tables"
            }
            
        except Exception as e:
            return {"error": f"Failed to get schema: {str(e)}"}
    
    def _get_database_connection(self):
        """Get database connection from configuration."""
        try:
            from langchain_community.utilities import SQLDatabase
            
            sql_config = self.config.get('sql', {})
            connection_string = sql_config.get('connection_string')
            tables = sql_config.get('tables', ['execution', 'testcase', 'run'])
            
            if not connection_string:
                return None
            
            params = urllib.parse.quote_plus(connection_string)
            return SQLDatabase.from_uri(
                f"mssql+pyodbc:///?odbc_connect={params}",
                include_tables=tables
            )
        except Exception:
            return None


class SQLQueryTool(BaseTool):
    """Tool for executing SQL queries."""

    @property
    def name(self) -> str:
        return "sql_query"

    @property
    def description(self) -> str:
        return "Execute SQL queries against the database. Input should be a SQL query string."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute SQL query.
        
        Args:
            input_data: Dict with 'query' key containing SQL query
                
        Returns:
            Dict with query results
        """
        try:
            query = input_data.get('query', '')
            if not query.strip():
                return {"error": "SQL query cannot be empty"}
            
            # Get database connection
            db = self._get_database_connection()
            if not db:
                return {"error": "Database connection not available"}
            
            # Execute the query
            result = db.run(query)
            
            return {
                "query": query,
                "result": result,
                "status": "success"
            }
            
        except Exception as e:
            return {"error": f"Failed to execute query: {str(e)}"}
    
    def _get_database_connection(self):
        """Get database connection from configuration."""
        try:
            from langchain_community.utilities import SQLDatabase
            
            sql_config = self.config.get('sql', {})
            connection_string = sql_config.get('connection_string')
            tables = sql_config.get('tables', ['execution', 'testcase', 'run'])
            
            if not connection_string:
                return None
            
            params = urllib.parse.quote_plus(connection_string)
            return SQLDatabase.from_uri(
                f"mssql+pyodbc:///?odbc_connect={params}",
                include_tables=tables
            )
        except Exception:
            return None


class SQLCheckerTool(BaseTool):
    """Tool for validating SQL queries before execution."""

    @property
    def name(self) -> str:
        return "sql_checker"

    @property
    def description(self) -> str:
        return "Validate SQL queries before execution. Input should be a SQL query string."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate SQL query.
        
        Args:
            input_data: Dict with 'query' key containing SQL query
                
        Returns:
            Dict with validation results
        """
        try:
            query = input_data.get('query', '')
            if not query.strip():
                return {"error": "SQL query cannot be empty"}
            
            # Get database connection
            db = self._get_database_connection()
            if not db:
                return {"error": "Database connection not available"}
            
            # Basic validation - check if query is safe
            query_lower = query.lower().strip()
            
            # Check for dangerous operations
            dangerous_keywords = ['drop', 'delete', 'truncate', 'alter', 'create', 'insert', 'update']
            for keyword in dangerous_keywords:
                if query_lower.startswith(keyword):
                    return {
                        "query": query,
                        "valid": False,
                        "error": f"Query contains potentially dangerous operation: {keyword.upper()}"
                    }
            
            # Check if it's a SELECT query
            if not query_lower.startswith('select'):
                return {
                    "query": query,
                    "valid": False,
                    "error": "Only SELECT queries are allowed"
                }
            
            # Try to validate syntax by preparing the query
            try:
                # This is a basic check - in production you might want more sophisticated validation
                db.run(f"SET NOEXEC ON; {query}; SET NOEXEC OFF;")
                return {
                    "query": query,
                    "valid": True,
                    "message": "Query syntax is valid"
                }
            except Exception as e:
                return {
                    "query": query,
                    "valid": False,
                    "error": f"Query syntax error: {str(e)}"
                }
            
        except Exception as e:
            return {"error": f"Failed to validate query: {str(e)}"}
    
    def _get_database_connection(self):
        """Get database connection from configuration."""
        try:
            from langchain_community.utilities import SQLDatabase
            
            sql_config = self.config.get('sql', {})
            connection_string = sql_config.get('connection_string')
            tables = sql_config.get('tables', ['execution', 'testcase', 'run'])
            
            if not connection_string:
                return None
            
            params = urllib.parse.quote_plus(connection_string)
            return SQLDatabase.from_uri(
                f"mssql+pyodbc:///?odbc_connect={params}",
                include_tables=tables
            )
        except Exception:
            return None
