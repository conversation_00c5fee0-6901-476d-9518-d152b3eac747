#!/usr/bin/env python3
"""
Test script for the Unified ReAct Agent.

This script tests the agent's functionality with GitLab, SQL, and file system operations.
"""

import os
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).resolve().parent / "src"))

from ai_agents.agents.registry import get_agent
from ai_agents.utils.config_loader import load_env_vars


def test_agent_creation():
    """Test that the agent can be created successfully."""
    print("🧪 Testing Agent Creation...")
    print("-" * 40)
    
    try:
        # Load environment variables
        env_vars = load_env_vars()
        
        # Check required environment variables
        required_vars = ["MISTRAL_API_KEY"]
        missing_vars = [var for var in required_vars if not env_vars.get(var)]
        
        if missing_vars:
            print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
            print("Please set these in your .env file")
            return False
        
        # Try to create the agent
        agent = get_agent("unified_react")
        print("✅ Agent created successfully!")
        print(f"   Agent type: {type(agent).__name__}")
        print(f"   Tools available: {len(agent.tools)}")
        print(f"   SQL enabled: {agent.db is not None}")
        
        # List available tools
        print("\n🔧 Available Tools:")
        for tool in agent.tools:
            print(f"   - {tool.name}: {tool.description[:60]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent creation failed: {str(e)}")
        return False


def test_simple_query():
    """Test a simple query to the agent."""
    print("\n🧪 Testing Simple Query...")
    print("-" * 40)
    
    try:
        agent = get_agent("unified_react")
        
        # Test with a simple query
        test_query = "What tools do you have available?"
        print(f"Query: {test_query}")
        
        result = agent.run({"query": test_query})
        
        print(f"✅ Query executed successfully!")
        print(f"Response: {result['response'][:200]}...")
        print(f"Status: {result['metadata']['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Query test failed: {str(e)}")
        return False


def test_gitlab_info_extraction():
    """Test the GitLab info extraction functionality."""
    print("\n🧪 Testing GitLab Info Extraction...")
    print("-" * 40)
    
    try:
        agent = get_agent("unified_react")
        
        # Test info extraction
        test_queries = [
            "Show me README.md from main branch",
            "Get SHA for config.py on develop branch",
            "What's in utils.py from main branch as it was on 2024-01-15T10:30:00Z?"
        ]
        
        for query in test_queries:
            print(f"\nTesting extraction for: {query}")
            extracted = agent._extract_info_from_query(query)
            print(f"Extracted: {extracted}")
        
        print("✅ Info extraction working!")
        return True
        
    except Exception as e:
        print(f"❌ Info extraction test failed: {str(e)}")
        return False


def main():
    """Run all tests."""
    print("🚀 Unified ReAct Agent Test Suite")
    print("=" * 50)
    
    tests = [
        ("Agent Creation", test_agent_creation),
        ("Simple Query", test_simple_query),
        ("GitLab Info Extraction", test_gitlab_info_extraction),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The agent is ready to use.")
        print("\n💡 To run the agent:")
        print("   python -m ai_agents.execution.cli unified_react --input '{\"query\": \"your question here\"}'")
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
