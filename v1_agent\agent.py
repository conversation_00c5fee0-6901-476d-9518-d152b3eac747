import re
import requests
import os
import urllib
import ast
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from langchain.agents import create_react_agent, AgentExecutor
from langchain.agents.react.base import DocstoreExplorer
from langchain_core.prompts import PromptTemplate
from langchain_core.tools import Tool
from langchain_core.language_models.llms import LLM
from langchain_core.callbacks.manager import CallbackManagerFor<PERSON>MRun
from langchain_core.language_models.chat_models import SimpleChatModel
from langchain_core.messages import HumanMessage, AIMessage
from langchain_community.utilities import SQLDatabase
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain.tools import tool
from pydantic import BaseModel, Field
import json
from tools import make_gitlab_tools, create_gitlab_config, GitLabAPIError
from file_tools import make_file_tools, create_file_config, FileSystemError
from mistralai import Mistral
from dotenv import load_dotenv
from config import GitLabConfig, SQLConfig, MistralConfig, AgentConfig, FileConfig

# Mistral API configuration (with fallback to config)
MISTRAL_API_KEY = MistralConfig.API_KEY
MISTRAL_API_URL = MistralConfig.API_URL
MISTRAL_MODEL = MistralConfig.GITLAB_MODEL


class MistralLLM(LLM):
    """Custom LangChain LLM wrapper for Mistral API (completion mode)"""

    api_key: str = Field(...)
    model: str = Field(default="mistral-medium")
    temperature: float = Field(default=0.0)
    max_tokens: int = Field(default=1000)

    def __init__(self, api_key: str, model: str = "mistral-medium", **kwargs):
        super().__init__(api_key=api_key, model=model, **kwargs)

    @property
    def _llm_type(self) -> str:
        return "mistral"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """Make a request to Mistral API"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        messages = [{"role": "user", "content": prompt}]

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

        if stop:
            data["stop"] = stop

        try:
            response = requests.post(MISTRAL_API_URL, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            return f"Error calling Mistral API: {str(e)}"


class CustomMistralLLM(SimpleChatModel, BaseModel):
    """Custom Mistral LLM wrapper with chat functionality for SQL operations"""
    client: Mistral
    model: str

    def __init__(self, api_key: str, model: str):
        super().__init__(client=Mistral(api_key=api_key), model=model)

    def _call(self, messages: List[HumanMessage], stop: Optional[List[str]] = None, **kwargs: Any) -> str:
        # Filter out LangChain-specific parameters that Mistral doesn't understand
        filtered_kwargs = {}
        for key, value in kwargs.items():
            if key not in ['run_manager', 'callbacks']:
                filtered_kwargs[key] = value

        filtered_kwargs["temperature"] = 0

        converted = []
        for m in messages:
            if isinstance(m, HumanMessage):
                converted.append({"role": "user", "content": m.content})
            elif isinstance(m, AIMessage):
                converted.append({"role": "assistant", "content": m.content})

        try:
            result = self.client.chat.complete(model=self.model, messages=converted, **filtered_kwargs)
            return result.choices[0].message.content
        except Exception as e:
            return f"Error calling Mistral API: {str(e)}"

    @property
    def _llm_type(self) -> str:
        return "my-chat-mistral"


# Configuration for SQL operations
class Config:
    """Centralized configuration for the application"""
    DB_TABLES = ["execution", "testcase", "run"]
    MAX_ITERATIONS = 150
    EXECUTION_TIMEOUT = 300.0
    SQL_DIALECT = "mssql"
    TOP_K = 5


def setup_environment():
    """Initialize environment variables"""
    load_dotenv()
    return os.getenv("MISTRAL_API_KEY")


def create_database_connection():
    """Create and return SQLDatabase connection using configuration"""
    from config import SQLConfig

    connection_string = SQLConfig.get_connection_string()
    params = urllib.parse.quote_plus(connection_string)

    return SQLDatabase.from_uri(
        f"mssql+pyodbc:///?odbc_connect={params}",
        include_tables=SQLConfig.TABLES
    )


@tool
def final_answer_tool_func(input: Union[str, List]) -> str:
    """Tool to format final output, handling list or string input"""
    print("[DEBUG FINAL ANSWER INPUT RAW]:", input)

    if isinstance(input, list):
        # Input is likely a list of rows from SQL query
        # Convert to a readable table format or JSON string

        # Simple table format:
        output_lines = []
        for row in input:
            # Convert row list to a pipe-separated string
            line = " | ".join(str(cell) for cell in row)
            output_lines.append(line)
        output = "\n".join(output_lines)

    elif isinstance(input, str):
        # Current cleaning logic for string input
        cleaned = []
        for line in input.strip().splitlines():
            line = line.strip()
            if line.startswith("|") and "|" in line:
                parts = [cell.strip() for cell in line.split("|") if cell.strip()]
                if parts:
                    cleaned.append(" | ".join(parts))
            elif line:
                cleaned.append(line)
        output = "\n".join(cleaned)
    else:
        # Fallback: convert to string
        output = str(input)

    print("[DEBUG FINAL ANSWER CLEANED]:", output)
    return output


class UnifiedReActAgent:
    """
    Unified LangChain ReAct agent for both GitLab file operations and SQL database queries.
    Uses reasoning and acting pattern to handle complex queries across both systems.
    """

    def __init__(self, gitlab_config: Dict[str, str], mistral_api_key: str = MISTRAL_API_KEY, mistral_model: str = MISTRAL_MODEL, enable_sql: bool = True):
        self.gitlab_config = gitlab_config
        self.enable_sql = enable_sql

        # Initialize LLMs for different purposes
        self.llm = MistralLLM(api_key=mistral_api_key, model=mistral_model)  # For GitLab operations

        # Initialize SQL database if enabled
        self.db = None
        if enable_sql:
            try:
                self.db = create_database_connection()
                self.sql_llm = CustomMistralLLM(api_key=mistral_api_key, model="mistral-large-latest")
            except Exception as e:
                print(f"Warning: Could not initialize SQL database: {e}")
                self.enable_sql = False

        # Create tools
        self.tools = self._create_tools()

        # Create the ReAct agent
        self.agent = self._create_react_agent()

        # Create agent executor with proper early_stopping_method
        from config import AgentConfig
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=AgentConfig.VERBOSE,
            handle_parsing_errors=True,
            max_iterations=AgentConfig.MAX_ITERATIONS,
            max_execution_time=AgentConfig.EXECUTION_TIMEOUT,
            early_stopping_method="force",
            return_intermediate_steps=True  # This helps with debugging
        )
    
    def _create_tools(self) -> List[Tool]:
        """Create both GitLab and SQL tools for the ReAct agent"""
        tools = []

        # GitLab tools
        get_sha_func, get_sha_ts_func, get_content_func = make_gitlab_tools(self.gitlab_config)

        def get_file_sha_wrapper(input_str: str) -> str:
            """Get SHA hash of a file at a specific commit reference

            Args:
                input_str: JSON string with keys 'commit_ref' and 'file_path'
                Example: '{"commit_ref": "main", "file_path": "README.md"}'
            """
            try:
                params = json.loads(input_str)
                commit_ref = params.get('commit_ref', 'main')
                file_path = params['file_path']
                return get_sha_func(commit_ref, file_path)
            except (json.JSONDecodeError, KeyError) as e:
                return f"Error parsing input: {str(e)}. Expected JSON with 'commit_ref' and 'file_path' keys."
            except GitLabAPIError as e:
                return f"GitLab API Error: {str(e)}"

        def get_file_sha_from_branch_ts_wrapper(input_str: str) -> str:
            """Get SHA hash of a file from latest commit on branch before timestamp

            Args:
                input_str: JSON string with keys 'branch', 'timestamp', and 'file_path'
                Example: '{"branch": "main", "timestamp": "2024-01-15T10:30:00Z", "file_path": "config.py"}'
            """
            try:
                params = json.loads(input_str)
                branch = params.get('branch', 'main')
                timestamp = params['timestamp']
                file_path = params['file_path']
                # Normalize timestamp
                timestamp = self._normalize_timestamp(timestamp)
                return get_sha_ts_func(branch, timestamp, file_path)
            except (json.JSONDecodeError, KeyError) as e:
                return f"Error parsing input: {str(e)}. Expected JSON with 'branch', 'timestamp', and 'file_path' keys."
            except GitLabAPIError as e:
                return f"GitLab API Error: {str(e)}"

        def get_file_content_wrapper(input_str: str) -> str:
            """Get file content using SHA hash

            Args:
                input_str: JSON string with key 'file_sha'
                Example: '{"file_sha": "abc123def456789..."}'
            """
            try:
                params = json.loads(input_str)
                file_sha = params['file_sha']
                return get_content_func(file_sha)
            except (json.JSONDecodeError, KeyError) as e:
                return f"Error parsing input: {str(e)}. Expected JSON with 'file_sha' key."
            except GitLabAPIError as e:
                return f"GitLab API Error: {str(e)}"

        def extract_info_wrapper(input_str: str) -> str:
            """Extract structured information from user query

            Args:
                input_str: User query string
                Example: "Show me README.md from main branch"
            """
            try:
                extracted = self._extract_info_from_query(input_str)
                return json.dumps(extracted, indent=2)
            except Exception as e:
                return f"Error extracting info: {str(e)}"

        # Add GitLab tools
        gitlab_tools = [
            Tool(
                name="get_file_sha",
                description="Get SHA hash of a file at a specific commit reference. Input should be JSON with 'commit_ref' and 'file_path' keys.",
                func=get_file_sha_wrapper
            ),
            Tool(
                name="get_file_sha_from_branch_timestamp",
                description="Get SHA hash of a file from latest commit on branch before a specific timestamp. Input should be JSON with 'branch', 'timestamp', and 'file_path' keys.",
                func=get_file_sha_from_branch_ts_wrapper
            ),
            Tool(
                name="get_file_content",
                description="Get file content using SHA hash. Input should be JSON with 'file_sha' key.",
                func=get_file_content_wrapper
            ),
            Tool(
                name="extract_info",
                description="Extract structured information (file paths, branches, timestamps, etc.) from user query. Input should be the user query string.",
                func=extract_info_wrapper
            )
        ]

        tools.extend(gitlab_tools)

        # Add File System tools (PCAP and Log files)
        try:
            file_config = create_file_config(FileConfig.PCAP_FOLDER, FileConfig.LOG_FOLDER)
            (list_pcap_func, list_log_func, get_pcap_info_func,
             get_log_content_func, search_logs_func) = make_file_tools(file_config)

            def list_pcap_files_wrapper(input_str: str = "*.pcap") -> str:
                """List PCAP files in the configured folder

                Args:
                    input_str: File pattern to match (default: "*.pcap")
                    Example: "*.pcap" or "*.pcapng"
                """
                try:
                    # Remove quotes and whitespace that the LLM might add
                    pattern = input_str.strip().strip("'\"") if input_str.strip() else "*.pcap"
                    return list_pcap_func(pattern)
                except Exception as e:
                    return f"Error listing PCAP files: {str(e)}"

            def list_log_files_wrapper(input_str: str = "*.log") -> str:
                """List log files in the configured folder

                Args:
                    input_str: File pattern to match (default: "*.log")
                    Example: "*.log" or "*.txt"
                """
                try:
                    # Remove quotes and whitespace that the LLM might add
                    pattern = input_str.strip().strip("'\"") if input_str.strip() else "*.log"
                    return list_log_func(pattern)
                except Exception as e:
                    return f"Error listing log files: {str(e)}"

            def get_pcap_info_wrapper(input_str: str) -> str:
                """Get information about a specific PCAP file

                Args:
                    input_str: PCAP filename
                    Example: "capture.pcap"
                """
                try:
                    # Remove quotes and whitespace that the LLM might add
                    filename = input_str.strip().strip("'\"")
                    if not filename:
                        return "Error: Filename cannot be empty"
                    return get_pcap_info_func(filename)
                except Exception as e:
                    return f"Error getting PCAP info: {str(e)}"

            def get_log_content_wrapper(input_str: str) -> str:
                """Get content from a log file

                Args:
                    input_str: JSON string with keys 'filename', optional 'max_lines', 'tail'
                    Example: '{"filename": "app.log", "max_lines": 500, "tail": true}'
                """
                try:
                    if input_str.startswith('{'):
                        params = json.loads(input_str)
                        filename = params['filename'].strip().strip("'\"")
                        max_lines = params.get('max_lines', FileConfig.MAX_LOG_LINES)
                        tail = params.get('tail', True)
                    else:
                        # Remove quotes and whitespace that the LLM might add
                        filename = input_str.strip().strip("'\"")
                        max_lines = FileConfig.MAX_LOG_LINES
                        tail = True

                    if not filename:
                        return "Error: Filename cannot be empty"
                    return get_log_content_func(filename, max_lines, tail)
                except (json.JSONDecodeError, KeyError) as e:
                    return f"Error parsing input: {str(e)}. Expected JSON with 'filename' key or just filename string."
                except Exception as e:
                    return f"Error getting log content: {str(e)}"

            def search_logs_wrapper(input_str: str) -> str:
                """Search for a term across all log files

                Args:
                    input_str: JSON string with keys 'search_term', optional 'max_results'
                    Example: '{"search_term": "error", "max_results": 50}'
                """
                try:
                    if input_str.startswith('{'):
                        params = json.loads(input_str)
                        search_term = params['search_term'].strip().strip("'\"")
                        max_results = params.get('max_results', FileConfig.MAX_SEARCH_RESULTS)
                    else:
                        # Remove quotes and whitespace that the LLM might add
                        search_term = input_str.strip().strip("'\"")
                        max_results = FileConfig.MAX_SEARCH_RESULTS

                    if not search_term:
                        return "Error: Search term cannot be empty"
                    return search_logs_func(search_term, max_results)
                except (json.JSONDecodeError, KeyError) as e:
                    return f"Error parsing input: {str(e)}. Expected JSON with 'search_term' key or just search term string."
                except Exception as e:
                    return f"Error searching logs: {str(e)}"

            # Add File System tools
            file_tools = [
                Tool(
                    name="list_pcap_files",
                    description="List PCAP files in the configured folder. Input should be a file pattern (e.g., '*.pcap', '*.pcapng').",
                    func=list_pcap_files_wrapper
                ),
                Tool(
                    name="list_log_files",
                    description="List log files in the configured folder. Input should be a file pattern (e.g., '*.log', '*.txt').",
                    func=list_log_files_wrapper
                ),
                Tool(
                    name="get_pcap_info",
                    description="Get detailed information about a specific PCAP file. Input should be the filename.",
                    func=get_pcap_info_wrapper
                ),
                Tool(
                    name="get_log_content",
                    description="Get content from a log file. Input should be JSON with 'filename' and optional 'max_lines', 'tail' keys, or just the filename.",
                    func=get_log_content_wrapper
                ),
                Tool(
                    name="search_logs",
                    description="Search for a term across all log files. Input should be JSON with 'search_term' and optional 'max_results' keys, or just the search term.",
                    func=search_logs_wrapper
                )
            ]

            tools.extend(file_tools)
        except Exception as e:
            print(f"Warning: Could not create file system tools: {e}")

        # Add SQL tools if database is available
        if self.enable_sql and self.db:
            try:
                sql_toolkit = SQLDatabaseToolkit(db=self.db, llm=self.sql_llm)
                sql_tools = sql_toolkit.get_tools()

                # Create wrapped SQL tools to handle quoted inputs
                wrapped_sql_tools = []
                for tool in sql_tools:
                    if tool.name == "sql_db_schema":
                        # Create a wrapper for the schema tool to handle quotes
                        original_tool = tool  # Capture the tool in closure
                        def schema_wrapper(input_str: str, orig_tool=original_tool) -> str:
                            """Wrapper for sql_db_schema that handles quoted table names"""
                            try:
                                # Remove quotes and extra whitespace
                                cleaned_input = input_str.strip().strip("'\"")
                                return orig_tool._run(cleaned_input)
                            except Exception as e:
                                return f"Error getting schema: {str(e)}"

                        wrapped_tool = Tool(
                            name=tool.name,
                            description=tool.description,
                            func=schema_wrapper
                        )
                        wrapped_sql_tools.append(wrapped_tool)
                    elif tool.name == "sql_db_query":
                        # Create a wrapper for the query tool to handle quotes
                        original_tool = tool  # Capture the tool in closure
                        def query_wrapper(input_str: str, orig_tool=original_tool) -> str:
                            """Wrapper for sql_db_query that handles quoted queries"""
                            try:
                                # Remove outer quotes but preserve inner quotes in SQL
                                cleaned_input = input_str.strip()
                                if (cleaned_input.startswith('"') and cleaned_input.endswith('"')) or \
                                   (cleaned_input.startswith("'") and cleaned_input.endswith("'")):
                                    cleaned_input = cleaned_input[1:-1]
                                return orig_tool._run(cleaned_input)
                            except Exception as e:
                                return f"Error executing query: {str(e)}"

                        wrapped_tool = Tool(
                            name=tool.name,
                            description=tool.description,
                            func=query_wrapper
                        )
                        wrapped_sql_tools.append(wrapped_tool)
                    elif tool.name == "sql_db_query_checker":
                        # Create a wrapper for the query checker tool
                        original_tool = tool  # Capture the tool in closure
                        def query_checker_wrapper(input_str: str, orig_tool=original_tool) -> str:
                            """Wrapper for sql_db_query_checker that handles quoted queries"""
                            try:
                                # Remove outer quotes but preserve inner quotes in SQL
                                cleaned_input = input_str.strip()
                                if (cleaned_input.startswith('"') and cleaned_input.endswith('"')) or \
                                   (cleaned_input.startswith("'") and cleaned_input.endswith("'")):
                                    cleaned_input = cleaned_input[1:-1]
                                return orig_tool._run(cleaned_input)
                            except Exception as e:
                                return f"Error checking query: {str(e)}"

                        wrapped_tool = Tool(
                            name=tool.name,
                            description=tool.description,
                            func=query_checker_wrapper
                        )
                        wrapped_sql_tools.append(wrapped_tool)
                    else:
                        # For other tools, use them as-is
                        wrapped_sql_tools.append(tool)

                # Add final answer tool for SQL operations
                wrapped_sql_tools.append(Tool(
                    name="final_answer_tool",
                    func=final_answer_tool_func,
                    description="Use this tool to provide the final answer to the user and stop.",
                    return_direct=True
                ))

                tools.extend(wrapped_sql_tools)
            except Exception as e:
                print(f"Warning: Could not create SQL tools: {e}")

        return tools
    
    def _create_react_agent(self):
        """Create the ReAct agent with custom prompt for both GitLab and SQL operations"""

        react_prompt = PromptTemplate.from_template("""
You are a unified assistant that can help with GitLab file operations, SQL database queries, and local file system operations (PCAP and log files).

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

IMPORTANT RULES:

For GitLab operations:
1. Always start by using the 'extract_info' tool to understand what the user is asking for
2. For getting file content, you typically need to:
   - First get the SHA hash using 'get_file_sha' or 'get_file_sha_from_branch_timestamp'
   - Then get the content using 'get_file_content'
3. Handle timestamps by normalizing them to ISO format (YYYY-MM-DDTHH:MM:SSZ)
4. Default branch is 'main' if not specified

For SQL operations:
1. Never make up or guess any value (e.g., `sha`, `status`, `duration`, etc.).
2. Start by using `sql_db_schema` to understand the database structure
3. Use `sql_db_query_checker` to verify any SQL query before running it.
4. Use `sql_db_query` to retrieve real data.
5. Use JOINs between tables only when needed and based on schema relationships.
6. When finished with SQL queries, use the `final_answer_tool` with the exact result.
7. Do NOT format SQL code using markdown or triple backticks.

For File System operations (PCAP and Log files):
1. Use `list_pcap_files` to see available PCAP files in the pcap folder
2. Use `list_log_files` to see available log files in the log folder
3. Use `get_pcap_info` to get detailed information about a specific PCAP file
4. Use `get_log_content` to read log file content (supports limiting lines and tail/head options)
5. Use `search_logs` to search for specific terms across all log files
6. File operations work with local file system, not GitLab repository

General rules:
7. If a tool returns an error, DO NOT retry the same action. Instead, explain the error in your Final Answer
8. Once you have the information requested, immediately provide the Final Answer - do not keep searching
9. If you cannot complete the task due to errors, explain what went wrong in your Final Answer

Examples of what you can help with:
GitLab:
- "Show me the content of README.md from main branch"
- "Get SHA for utils.py on develop branch"
- "What's in config.py from main branch as it was on 2024-01-15T10:30:00Z?"

SQL:
- "Show all executions for testcase ID 1"
- "What are the most recent commit references for testcase ID 3?"
- "Retrieve result, duration, and timestamp for execution of testcase ID 2 in run ID 2"

File System:
- "List all PCAP files in the pcap folder"
- "Show me the content of the latest log file"
- "Search for 'error' in all log files"
- "Get information about capture.pcap file"
- "Show me the last 100 lines of app.log"

Begin!

Question: {input}
Thought: {agent_scratchpad}
""")

        return create_react_agent(self.llm, self.tools, react_prompt)
    
    def _extract_info_from_query(self, query: str) -> Dict[str, Any]:
        """Extract information from user query using regex patterns"""
        patterns = {
            'sha': r'([a-f0-9]{40}|[a-f0-9]{7,})',  # SHA hash (full or short)
            'branch': r'(?:branch|on|from)\s+([a-zA-Z0-9_\-\/]+)',
            'timestamp': r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z?|\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4})',
            'file_path': r'(?:file|path)\s+([a-zA-Z0-9_\-\/\.]+)|([a-zA-Z0-9_\-\/\.]+\.[a-zA-Z]+)',
            'commit_ref': r'(?:commit|ref)\s+([a-zA-Z0-9_\-\/]+)',
        }
        
        extracted = {}
        
        for key, pattern in patterns.items():
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                extracted[key] = match.group(1) if match.group(1) else match.group(2)
        
        # Try to infer file path from common extensions if not explicitly found
        if 'file_path' not in extracted:
            file_extensions = r'([a-zA-Z0-9_\-\/]+\.(?:py|js|json|md|txt|yml|yaml|xml|html|css|java|cpp|c|h))'
            match = re.search(file_extensions, query, re.IGNORECASE)
            if match:
                extracted['file_path'] = match.group(1)
        
        # Determine intent
        query_lower = query.lower()
        if any(word in query_lower for word in ['content', 'show', 'read', 'display', 'what', 'contains']):
            extracted['intent'] = 'get_content'
        elif any(word in query_lower for word in ['sha', 'hash', 'id']):
            extracted['intent'] = 'get_sha'
        elif 'file_path' in extracted:
            extracted['intent'] = 'get_content'  # Default to content if file path found
        else:
            extracted['intent'] = 'unknown'
        
        return extracted
    
    def _normalize_timestamp(self, timestamp: str) -> str:
        """Normalize timestamp to ISO format"""
        # Handle different timestamp formats
        if '/' in timestamp:
            # MM/DD/YYYY format
            try:
                dt = datetime.strptime(timestamp, '%m/%d/%Y')
                return dt.strftime('%Y-%m-%dT00:00:00Z')
            except ValueError:
                pass
        elif 'T' not in timestamp and len(timestamp) == 10:
            # YYYY-MM-DD format
            return f"{timestamp}T00:00:00Z"
        elif not timestamp.endswith('Z'):
            # Add Z if missing
            return f"{timestamp}Z"
        
        return timestamp
    
    def process_query_debug(self, user_input: str) -> Dict[str, Any]:
        """Process a user query with detailed debugging information"""
        try:
            result = self.agent_executor.invoke({"input": user_input})
            
            debug_info = {
                "output": result["output"],
                "intermediate_steps": result.get("intermediate_steps", []),
                "num_iterations": len(result.get("intermediate_steps", [])),
                "hit_limit": len(result.get("intermediate_steps", [])) >= 10,
                "status": "success"
            }
            
            return debug_info
        except Exception as e:
            return {
                "output": f"Error processing query: {str(e)}",
                "intermediate_steps": [],
                "num_iterations": 0,
                "hit_limit": False,
                "status": "error",
                "error": str(e)
            }

    def process_query(self, user_input: str) -> str:
        """Process a user query using the ReAct agent"""
        try:
            result = self.agent_executor.invoke({"input": user_input})
            
            # Check if the agent hit the iteration limit
            if "intermediate_steps" in result:
                if len(result["intermediate_steps"]) >= 10:  # or whatever your max_iterations is
                    return f"The agent reached the maximum number of iterations. Last output: {result.get('output', 'No output available')}"
            
            return result["output"]
        except Exception as e:
            return f"Error processing query: {str(e)}"
    
    def process_queries_batch(self, queries: List[str]) -> List[Dict[str, str]]:
        """Process multiple queries and return results"""
        results = []
        for query in queries:
            try:
                response = self.process_query(query)
                results.append({
                    "query": query,
                    "response": response,
                    "status": "success"
                })
            except Exception as e:
                results.append({
                    "query": query,
                    "response": f"Error: {str(e)}",
                    "status": "error"
                })
        return results


# Example usage and testing
def main():
    # Load configuration from config.py
    from config import GitLabConfig, MistralConfig, AgentConfig, validate_config, print_config_summary

    try:
        # Validate configuration
        validate_config()
        print_config_summary()

        # GitLab configuration
        gitlab_config = GitLabConfig.to_dict()

        # Create Unified ReAct agent
        agent = UnifiedReActAgent(
            gitlab_config=gitlab_config,
            mistral_api_key=MistralConfig.API_KEY,
            mistral_model=MistralConfig.MODEL,
            enable_sql=AgentConfig.ENABLE_SQL
        )

    except ValueError as e:
        print(f"❌ Configuration Error: {e}")
        print("Please check your configuration in config.py or .env file")
        return
    except Exception as e:
        print(f"❌ Initialization Error: {e}")
        return

    # Example queries for both GitLab and SQL
    test_queries = [
        
    ]

    print("Unified GitLab & SQL ReAct Agent - Interactive Demo")
    print("=" * 60)

    for query in test_queries:
        print(f"\nQuery: {query}")
        print("-" * 40)
        response = agent.process_query(query)
        print(f"Response: {response}")
        print()

    # Interactive mode
    print("\n" + "=" * 60)
    print("Interactive Mode - Type 'quit' to exit")
    print("You can ask about GitLab files or SQL database queries")
    print("=" * 60)

    while True:
        try:
            user_input = input("\nYour query: ")
            if user_input.lower() in ['quit', 'exit', 'q']:
                break

            response = agent.process_query(user_input)
            print(f"\nResponse: {response}")

        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()