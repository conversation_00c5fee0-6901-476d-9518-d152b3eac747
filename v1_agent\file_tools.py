"""
Local File Tools for PCAP and Log File Operations

This module provides tools for retrieving and analyzing PCAP files and log files
from local directories. These tools integrate with the unified agent to provide
file system access alongside GitLab and SQL capabilities.
"""

import os
import glob
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
import mimetypes


class FileSystemError(Exception):
    """Custom exception for file system errors"""
    pass


def list_pcap_files(pcap_folder: str = "pcap", pattern: str = "*.pcap") -> List[Dict[str, Any]]:
    """
    List all PCAP files in the specified folder.

    Args:
        pcap_folder: Path to the PCAP folder (default: "pcap")
        pattern: File pattern to match (default: "*.pcap")

    Returns:
        List of dictionaries containing file information

    Raises:
        FileSystemError: If the folder doesn't exist or access fails
    """
    try:
        if not os.path.exists(pcap_folder):
            raise FileSystemError(f"PCAP folder '{pcap_folder}' does not exist")

        if not os.path.isdir(pcap_folder):
            raise FileSystemError(f"'{pcap_folder}' is not a directory")

        # Get all PCAP files matching the pattern
        pcap_files = []

        if pattern == "*.pcap":
            # Default behavior: search for all common PCAP extensions
            pcap_patterns = ["*.pcap", "*.pcapng", "*.cap"]
            for pcap_pattern in pcap_patterns:
                search_pattern = os.path.join(pcap_folder, pcap_pattern)
                pcap_files.extend(glob.glob(search_pattern))
        else:
            # Use the specific pattern provided
            search_pattern = os.path.join(pcap_folder, pattern)
            pcap_files = glob.glob(search_pattern)

        file_list = []
        for file_path in pcap_files:
            try:
                stat_info = os.stat(file_path)
                file_info = {
                    "filename": os.path.basename(file_path),
                    "full_path": os.path.abspath(file_path),
                    "size_bytes": stat_info.st_size,
                    "size_mb": round(stat_info.st_size / (1024 * 1024), 2),
                    "created_time": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                    "extension": os.path.splitext(file_path)[1].lower()
                }
                file_list.append(file_info)
            except OSError as e:
                print(f"Warning: Could not get info for {file_path}: {e}")
                continue

        # Sort by modification time (newest first)
        file_list.sort(key=lambda x: x["modified_time"], reverse=True)

        return file_list

    except Exception as e:
        raise FileSystemError(f"Failed to list PCAP files: {str(e)}")


def list_log_files(log_folder: str = "log", pattern: str = "*.log") -> List[Dict[str, Any]]:
    """
    List all log files in the specified folder.
    
    Args:
        log_folder: Path to the log folder (default: "log")
        pattern: File pattern to match (default: "*.log")
        
    Returns:
        List of dictionaries containing file information
        
    Raises:
        FileSystemError: If the folder doesn't exist or access fails
    """
    try:
        if not os.path.exists(log_folder):
            raise FileSystemError(f"Log folder '{log_folder}' does not exist")
        
        if not os.path.isdir(log_folder):
            raise FileSystemError(f"'{log_folder}' is not a directory")
        
        # Get all log files matching the pattern
        search_pattern = os.path.join(log_folder, pattern)
        log_files = glob.glob(search_pattern)
        
        # Also check for common log extensions
        additional_patterns = ["*.txt", "*.out", "*.err"]
        for add_pattern in additional_patterns:
            additional_search = os.path.join(log_folder, add_pattern)
            log_files.extend(glob.glob(additional_search))
        
        # Remove duplicates
        log_files = list(set(log_files))
        
        file_list = []
        for file_path in log_files:
            try:
                stat_info = os.stat(file_path)
                file_info = {
                    "filename": os.path.basename(file_path),
                    "full_path": os.path.abspath(file_path),
                    "size_bytes": stat_info.st_size,
                    "size_kb": round(stat_info.st_size / 1024, 2),
                    "created_time": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                    "extension": os.path.splitext(file_path)[1].lower()
                }
                file_list.append(file_info)
            except OSError as e:
                print(f"Warning: Could not get info for {file_path}: {e}")
                continue
        
        # Sort by modification time (newest first)
        file_list.sort(key=lambda x: x["modified_time"], reverse=True)
        
        return file_list
        
    except Exception as e:
        raise FileSystemError(f"Failed to list log files: {str(e)}")


def get_pcap_file_info(filename: str, pcap_folder: str = "pcap") -> Dict[str, Any]:
    """
    Get detailed information about a specific PCAP file.
    
    Args:
        filename: Name of the PCAP file
        pcap_folder: Path to the PCAP folder (default: "pcap")
        
    Returns:
        Dictionary containing detailed file information
        
    Raises:
        FileSystemError: If the file doesn't exist or access fails
    """
    try:
        file_path = os.path.join(pcap_folder, filename)
        
        if not os.path.exists(file_path):
            raise FileSystemError(f"PCAP file '{filename}' not found in '{pcap_folder}'")
        
        if not os.path.isfile(file_path):
            raise FileSystemError(f"'{filename}' is not a file")
        
        stat_info = os.stat(file_path)
        
        file_info = {
            "filename": filename,
            "full_path": os.path.abspath(file_path),
            "size_bytes": stat_info.st_size,
            "size_mb": round(stat_info.st_size / (1024 * 1024), 2),
            "created_time": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
            "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
            "accessed_time": datetime.fromtimestamp(stat_info.st_atime).isoformat(),
            "extension": os.path.splitext(file_path)[1].lower(),
            "mime_type": mimetypes.guess_type(file_path)[0],
            "is_readable": os.access(file_path, os.R_OK),
            "folder": pcap_folder
        }
        
        return file_info
        
    except Exception as e:
        raise FileSystemError(f"Failed to get PCAP file info: {str(e)}")


def get_log_file_content(filename: str, log_folder: str = "log", 
                        max_lines: int = 1000, tail: bool = True) -> Dict[str, Any]:
    """
    Get content from a log file with options for limiting output.
    
    Args:
        filename: Name of the log file
        log_folder: Path to the log folder (default: "log")
        max_lines: Maximum number of lines to return (default: 1000)
        tail: If True, return last N lines; if False, return first N lines
        
    Returns:
        Dictionary containing file content and metadata
        
    Raises:
        FileSystemError: If the file doesn't exist or access fails
    """
    try:
        file_path = os.path.join(log_folder, filename)
        
        if not os.path.exists(file_path):
            raise FileSystemError(f"Log file '{filename}' not found in '{log_folder}'")
        
        if not os.path.isfile(file_path):
            raise FileSystemError(f"'{filename}' is not a file")
        
        if not os.access(file_path, os.R_OK):
            raise FileSystemError(f"No read permission for '{filename}'")
        
        # Get file info
        stat_info = os.stat(file_path)
        
        # Read file content
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        
        # Apply line limiting
        if total_lines > max_lines:
            if tail:
                content_lines = lines[-max_lines:]
                truncated_info = f"Showing last {max_lines} lines of {total_lines} total lines"
            else:
                content_lines = lines[:max_lines]
                truncated_info = f"Showing first {max_lines} lines of {total_lines} total lines"
        else:
            content_lines = lines
            truncated_info = f"Showing all {total_lines} lines"
        
        content = ''.join(content_lines)
        
        result = {
            "filename": filename,
            "full_path": os.path.abspath(file_path),
            "content": content,
            "total_lines": total_lines,
            "returned_lines": len(content_lines),
            "truncated": total_lines > max_lines,
            "truncated_info": truncated_info,
            "size_bytes": stat_info.st_size,
            "size_kb": round(stat_info.st_size / 1024, 2),
            "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
            "encoding": "utf-8"
        }
        
        return result
        
    except UnicodeDecodeError:
        raise FileSystemError(f"Cannot decode '{filename}' as text file")
    except Exception as e:
        raise FileSystemError(f"Failed to read log file: {str(e)}")


def search_log_files(search_term: str, log_folder: str = "log", 
                     max_results: int = 100) -> List[Dict[str, Any]]:
    """
    Search for a term across all log files in the folder.
    
    Args:
        search_term: Term to search for
        log_folder: Path to the log folder (default: "log")
        max_results: Maximum number of matching lines to return
        
    Returns:
        List of dictionaries containing search results
        
    Raises:
        FileSystemError: If the folder doesn't exist or access fails
    """
    try:
        if not search_term.strip():
            raise FileSystemError("Search term cannot be empty")
        
        log_files = list_log_files(log_folder)
        results = []
        
        for file_info in log_files:
            try:
                with open(file_info["full_path"], 'r', encoding='utf-8', errors='ignore') as f:
                    for line_num, line in enumerate(f, 1):
                        if search_term.lower() in line.lower():
                            results.append({
                                "filename": file_info["filename"],
                                "line_number": line_num,
                                "line_content": line.strip(),
                                "file_path": file_info["full_path"],
                                "file_modified": file_info["modified_time"]
                            })
                            
                            if len(results) >= max_results:
                                break
                
                if len(results) >= max_results:
                    break
                    
            except Exception as e:
                print(f"Warning: Could not search in {file_info['filename']}: {e}")
                continue
        
        return results
        
    except Exception as e:
        raise FileSystemError(f"Failed to search log files: {str(e)}")


# Configuration and tool factory functions
def create_file_config(pcap_folder: str = "pcap", log_folder: str = "log") -> Dict[str, str]:
    """
    Create a configuration dictionary for file operations.
    
    Args:
        pcap_folder: Path to the PCAP folder
        log_folder: Path to the log folder
        
    Returns:
        Dict containing the configuration
    """
    return {
        'pcap_folder': pcap_folder,
        'log_folder': log_folder
    }


def make_file_tools(config: Dict[str, str]):
    """
    Create file tool functions with pre-configured settings.
    
    Args:
        config: Configuration dictionary from create_file_config()
        
    Returns:
        Tuple of file operation functions ready to use as LangChain tools
    """
    
    def tool_list_pcap_files(pattern: str = "*.pcap") -> str:
        """Tool: List PCAP files in the configured folder"""
        try:
            files = list_pcap_files(config['pcap_folder'], pattern)
            return json.dumps(files, indent=2)
        except FileSystemError as e:
            return f"Error: {str(e)}"
    
    def tool_list_log_files(pattern: str = "*.log") -> str:
        """Tool: List log files in the configured folder"""
        try:
            files = list_log_files(config['log_folder'], pattern)
            return json.dumps(files, indent=2)
        except FileSystemError as e:
            return f"Error: {str(e)}"
    
    def tool_get_pcap_info(filename: str) -> str:
        """Tool: Get PCAP file information"""
        try:
            info = get_pcap_file_info(filename, config['pcap_folder'])
            return json.dumps(info, indent=2)
        except FileSystemError as e:
            return f"Error: {str(e)}"
    
    def tool_get_log_content(filename: str, max_lines: int = 1000, tail: bool = True) -> str:
        """Tool: Get log file content"""
        try:
            content = get_log_file_content(filename, config['log_folder'], max_lines, tail)
            return json.dumps(content, indent=2)
        except FileSystemError as e:
            return f"Error: {str(e)}"
    
    def tool_search_logs(search_term: str, max_results: int = 100) -> str:
        """Tool: Search across log files"""
        try:
            results = search_log_files(search_term, config['log_folder'], max_results)
            return json.dumps(results, indent=2)
        except FileSystemError as e:
            return f"Error: {str(e)}"
    
    return (tool_list_pcap_files, tool_list_log_files, tool_get_pcap_info, 
            tool_get_log_content, tool_search_logs)
